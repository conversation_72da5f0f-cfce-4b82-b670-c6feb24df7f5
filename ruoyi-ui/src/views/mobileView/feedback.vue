<template>
  <div class="mobilefeedback-container">
    <!-- 反馈内容输入框 -->

    <el-form ref="feedbackForm" :model="form">
      <div class="labelText">反馈内容:</div>
      <el-form-item label="">
        <el-input
          v-model="form.feedbackContent"
          type="textarea"
          placeholder="请您详细描述您的反馈内容"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <div class="labelText">
        上传图片
        <span>（最多可上传5张）：</span>
      </div>
      <!-- 上传图片 -->
      <el-form-item>
        <el-upload
          action="#"
          list-type="picture-card"
          accept=".jpg,.png,.gif,.jpeg"
          :auto-upload="false"
          :file-list="images"
          :limit="5"
          :on-exceed="handleExceed"
          :on-change="handleFileChange"
          :on-remove="handleRemove"
          class="upload-demo"
        >
          <i class="el-icon-plus" />
        </el-upload>
      </el-form-item>

      <!-- 提交按钮 -->
      <el-form-item>
        <el-button
          class="submit-btn"
          type="primary"
          style="width: 90%"
          :loading="isSubmitting"
          @click="submitFeedback"
        >{{ isSubmitting ? '提交中...' : '提交' }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import CryptoJS from 'crypto-js'
import { uploadImgRequest, feedbackRequest } from '@/api/mobile'
import { getstaffId } from '@/views/mobileView/utlis'
import { eventBus } from '@/utils/eventBus'
import { debounce } from '@/utils'

export default {
  name: 'Feedback',
  data() {
    return {
      staffId: getstaffId(),
      form: {
        feedbackContent: '' // 反馈内容
      },
      images: [], // 已上传图片列表
      isSubmitting: false, // 防重复提交标志
      debouncedSubmitFeedback: null // 防抖函数
    }
  },
  created() {
    // 创建防抖函数，延迟500ms
    this.debouncedSubmitFeedback = debounce(this.submitFeedbackInternal.bind(this), 500)
  },
  methods: {
    // 处理文件选择事件
    handleFileChange(file, fileList) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.images = fileList
        // 这里可以添加实际上传逻辑
        // console.log('文件内容：', e.target.result)
        // 获取文件md5
        const md5 = CryptoJS.MD5(e.target.result).toString()
        console.log('文件md5：', md5)
        this.uploadAvatar(md5, file.raw)
      }
      reader.readAsDataURL(file.raw)
    },

    // 实际上传方法 (示例)
    async uploadAvatar(md5, file) {
      try {
        // 这里替换为你的实际上传API
        console.log('file', file)
        const formData = new FormData()
        formData.append('file', file)
        const response = await uploadImgRequest(md5, formData)
        console.log(response)
        this.$message.success({ message: '上传成功', duration: 3000 })
        this.images[this.images.length - 1].id = response.data.id
      } catch (error) {
        console.log(error)
        this.$message.warning({ message: error, duration: 3000 })
        // 删除掉最新添加的
        this.images.pop()
        console.error(error)
      }
    },
    // 文件超出限制时的处理
    handleExceed(files, fileList) {
      this.$message.warning({ message: '最多只能上传 5 张图片！', duration: 3000 })
    },
    // 移除图片
    handleRemove(file, fileList) {
      this.images = fileList
      console.log(file, fileList)
      // this.imagesIds = imagesIds.map(item => item);
    },
    // 提交反馈
    async submitFeedback() {
      // 防重复提交检查
      if (this.isSubmitting) {
        console.log('正在提交中，请稍候...')
        return
      }
      
      // 使用防抖函数处理提交
      this.debouncedSubmitFeedback()
    },
    
    // 实际的提交反馈逻辑
    async submitFeedbackInternal() {
      // 设置提交状态
      this.isSubmitting = true
      
      try {
        if (this.form.feedbackContent === '') {
          this.$message.closeAll()
          this.$message.warning({ message: '请填写反馈内容！', duration: 3000 })
          return
        }
        const data = {
          userCode: this.staffId,
          feedbackContent: this.form.feedbackContent,
          imageIds: this.images.map((img) => img.id).join(','),
          visitFrom: localStorage.getItem('visitFrom')
        }
        const res = await feedbackRequest(data)
        if (res.code !== 200) {
          this.$message.error({ message: res.msg, duration: 3000 })
          return
        }
        this.$message.success({ message: '提交成功！', duration: 3000 })
        // 触发积分刷新事件
        eventBus.$emit('refreshPoints')
        // 返回上一级页面
        this.$router.go(-1)
        console.log(res)

        // 清空表单
        this.form.feedbackContent = ''
        this.images = []
      } catch (error) {
        console.error('提交反馈失败:', error)
        this.$message.error({ message: '提交失败，请稍后重试', duration: 3000 })
      } finally {
        // 重置提交状态
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.mobilefeedback-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100vw;
  height: 100vh;
  padding: 16px;
  box-sizing: border-box;

  .el-form {
    width: 100%;
  }
  .labelText {
    font-size: 16px;
    margin-bottom: 12px;
    span {
      font-size: 12px;
      color: #c0c0c0;
    }
  }
  ::v-deep .el-upload--picture-card {
    width: 64px;
    height: 64px;
    line-height: 64px;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 64px;
    height: 64px;
  }
  .submit-btn {
    position: fixed;
    bottom: 30px;
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
  }
  ::v-deep .el-button--primary {
    color: #4cb848;
    background-color: #ebf5e8;
    border: none;
  }
  ::v-deep .upload-demo {
    .el-upload--picture-card {
      border: 1px dashed #d9d9d9;
      background-color: #fafafa;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .el-upload-list--picture-card .el-upload-list__item {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      width: 64px;
      height: 64px;
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
}
</style>
