import TextMsgClass from '@/views/TextMsgClass'
import TextCompletions from '@/views/TextCompletions'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import FileCompletions from '@/views/FileCompletions'
import FileMsgClass from '@/views/FileMsgClass'
import { generateChatId } from '@/utils/deepseek'
import { MSG_TYPE } from '../utils/constant'
import { MessageBox } from 'element-ui'
import { getToken, getAccess_token } from '@/utils/auth'
import { getHistories, searchfuncInfo } from '@/api/deepSeekApi'
import { set } from 'nprogress'
import { eventBus } from '@/utils/eventBus'
/**  */
const appId =
  process.env.NODE_ENV === 'production' ? '67a86833ee92f8ff6e2ccebe' : '67c811aa870793870db6010a'
const baseUrl = process.env.VUE_APP_BASE_API
export default {
  data() {
    return {
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      baseUrl: baseUrl,
      textList: '',
      textarea: '',
      funTitle: '',
      msgType: 'common',
      ctrl: null, // 终止fetchEventSource请求的变量
      showHelpRead: false, // 是否显示语音助读终止按钮
      isThinking: false, // 是否正在思考
      startReading: false // 开始语音助读
    }
  },
  methods: {
    /** 终止sse请求 */
    stopFetchSource() {
      this.isThinking = false
      if (this.ctrl) {
        console.log('主动终止sse请求', this.ctrl)
        // this.ctrl.stop()
        this.ctrl.abort()
        this.ctrl = null
      }
    },
    /** 发送消息按钮 */
    async sendClick(type) {
      // 每次发送新内容时 重置滚动flag状态 并且滚动到最底部
      this.autoScroll = true
      this.scrollToBottom('usesse 45')

      this.showTranslateBar = false
      this.isShowFunBox = false
      this.excelbox = false
      if (type === 'stop') {
        this.sendClickContent(type)
      } else if (this.online) {
        searchfuncInfo().then((res) => {
          if (res.code === 200) {
            if (res.data.status === 1) {
              MessageBox.alert(
                `尊敬的用户，今日联网搜索次数已达上限。搜索次数将于每日零点自动重置，届时欢迎您继续使用。`,
                `联网次数用尽`,
                {
                  dangerouslyUseHTMLString: true,
                  showConfirmButton: false,
                  customClass: this.disabledTrigger ? 'mobile-message-box' : '',
                  callback: () => {
                    this.online = 0
                    this.sendClickContent(type)
                  }
                }
              )
            } else {
              this.sendClickContent(type)
            }
          }
        })
      } else {
        this.sendClickContent(type)
      }
    },
    async sendClickContent(type) {
      // console.log(type, this.textarea, this.fileList.length, '什么情况')
      if (!type && !this.textarea.trim() && this.fileList.length === 0) {
        return
      }
      // 如果正在思考中，则主动停止
      if (this.isThinking) {
        // this.$message.warning("正在聊天中...请等待结束");
        // 停止思考的时候主动重置标题参数
        if (!this.isShowExcelBox) {
          this.funTitle = ''
        }
        this.stopFetchSource()
        this.isThinking = false
        const length = this.msgList.length
        this.msgList = this.msgList.map((item, i) => {
          if (i === length - 1) {
            item.status = ''
          }

          return item
        })
        // console.log(this.msgList, '什么意思Stop')
        return
      }
      // 确保msgType设置逻辑与上传时一致
      if (this.funTitle && MSG_TYPE[this.funTitle]) {
        this.msgType = MSG_TYPE[this.funTitle]
      } else if (this.fileList && this.fileList.length) {
        this.msgType = 'common'
      }
      // 如果使用了提示词
      if (this.textarea !== '' && this.textList.length > 0) {
        this.msgType = 'doctips'
      }
      // console.log('msgType:', this.msgType)
      // console.log('shortcut check')
      if (params) {
        params.bizType = this.msgType
        console.log('设置bizType:', params.bizType)
      }
      if (
        ['中英互译', '文档校对', '文档总结', '文档阅读', '提示词帮助', 'excel助手'].includes(
          this.textarea.trim()
        ) &&
        this.fileList.length
      ) {
        this.usedShortcutInCurSessionNumber = this.usedShortcutInCurSessionNumber + 1
      }
      if (this.usedShortcutInCurSessionNumber > 1 || this.showFirst) {
        console.log(
          1,
          'usedShortcutInCurSessionNumber',
          this.usedShortcutInCurSessionNumber,
          'showFirst',
          this.showFirst,
          'disabledTrigger',
          this.disabledTrigger
        )
        if (this.disabledTrigger) {
          console.log(
            2,
            'usedShortcutInCurSessionNumber',
            this.usedShortcutInCurSessionNumber,
            'showFirst',
            this.showFirst,
            'disabledTrigger',
            this.disabledTrigger
          )
          await this.initChat()
        } else {
          console.log(
            3,
            'usedShortcutInCurSessionNumber',
            this.usedShortcutInCurSessionNumber,
            'showFirst',
            this.showFirst,
            'disabledTrigger',
            this.disabledTrigger
          )
          await this.getChatHistoryList(1)
          await this.newChatClick('129')
        }
        await this.sendClick()
        return
      }
      const dataId = generateChatId(24)
      const chartId = this.disabledTrigger ? this.activeChatId : this.curChat.chatId
      // 创建human message放入msgList中
      // const msg = new TextMsgClass(dataId, 'Human', this.textarea)
      const msg = new FileMsgClass(dataId, 'Human', this.textarea, this.fileList)
      console.log('msg', msg)
      this.msgList.push(msg)
      // 组装上传参数
      let params = ''
      // todo
      if (this.textarea && this.fileList.length > 0) {
        // 文字+文件
        params = new FileCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          // "shareChat-1741425008504-wybMmtwWk9k3KdEATvwOLRht",
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          this.fileList,
          null,
          this.translateFrom,
          this.translateTo
        )
        // params.messages[0].content.unshift({
        //   type: 'text',
        //   text: this.textarea
        // })
      } else if (this.textarea && this.fileList.length === 0) {
        // 纯文字
        params = new TextCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid')
        )
      } else if (!this.textarea && this.fileList.length > 0) {
        // 纯文件
        params = new FileCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          // "shareChat-1741425008504-wybMmtwWk9k3KdEATvwOLRht",
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          this.fileList,
          null,
          this.translateFrom,
          this.translateTo
        )
      }
      params.bizType = this.msgType
      if (this.isElectronic) {
        params.dtyFile = 'DTY'
      } else {
        params.dtyFile = ''
      }
      // 组装上传参数END
      console.log('params', params)
      this.sseReq(params)
      setTimeout(() => {
        eventBus.$emit('refreshPoints')
      }, 200)
    },
    async sseReq(params) {
      params.retainDatasetCite = true

      params.variables = {
        llm_type: this.llm_type,
        search_func: this.online
      }
      params.staffId = localStorage.getItem('staffId')
      params.visitFrom = localStorage.getItem('visitFrom')
      this.scrollToBottom('usesee 205')
      this.textarea = ''
      this.textList = ''
      this.fileList = []
      this.msgType = 'common'
      this.isThinking = true
      if (this.funTitle === '文档阅读') {
        this.showHelpRead = true
      }
      /** * 获取任务列表-SSE传输模式 */
      const aiMessage = new TextMsgClass(params.responseChatItemId, 'AI', '', '', 'thinking')
      console.log(aiMessage, 'aiMessage')
      this.msgList.push(aiMessage)
      this.ctrl = new AbortController()
      const _this = this
      let token = ''
      // 非开发环境
      // console.log('process.env.NODE_ENV', process.env.NODE_ENV)
      // if (process.env.NODE_ENV !== 'development') {
      token =
        'Bearer ' +
          (localStorage.getItem('token') || getToken() || sessionStorage.getItem('token')) ||
        getToken() ||
        getAccess_token()
      // console.log('token', token)
      // }

      await fetchEventSource(_this.baseUrl + '/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params),
        signal: this.ctrl.signal,
        openWhenHidden: true, // 页面隐藏时保持连接
        onmessage(msg) {
          console.log('SSE接收信息 链接')
          _this.handleChunk(msg, aiMessage)
        },
        onclose() {
          console.log('SSE接收信息 断开')
          _this.stopFetchSource()
        },
        onerror(err) {
          console.log('SSE接收信息 异常')
          // 在调用流的时候可能遇到 sse流504错误 实际上这个流已经断开了
          aiMessage.status = 'error'
          _this.stopFetchSource()
          throw err
        }
      })
    },
    // 播放语音
    async playAudio() {
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel() // 停止当前播放
      }
      if (this.speechList.length && !this.startReading) {
        this.startReading = true // 标记正在播放
        const text = this.productSegments('').replace(/[#]/g, '') // 从队列中取出一段文字
        const msg = new SpeechSynthesisUtterance(text)
        const voices = window?.speechSynthesis?.getVoices?.() || [] // 获取语言包
        const voice = voices.find((item) => item.lang === 'zh-CN')
        msg.rate = 1
        if (voice) {
          msg.voice = voice
        }
        msg.onend = () => {
          this.startReading = false // 标记播放结束
          this.playAudio() // 递归调用播放下一段
        }
        msg.onerror = () => {
          console.error('语音播放出错')
          this.startReading = false // 标记播放结束
          if (this.showHelpRead) {
            this.playAudio() // 继续播放下一段
          }
        }
        window.speechSynthesis.speak(msg)
      }
    },
    handleChunk(chunk, aiMessage) {
      // 处理每一部分数据，例如更新消息列表
      const { event, data } = chunk
      // console.log('chunk', event, data)
      const parsedData = data !== '[DONE]' ? JSON.parse(data) : null
      // 联网次数用尽
      // if (event === 'searchFunc') {
      //   this.stopFetchSource()
      //   MessageBox.alert(`尊敬的用户，今日联网搜索次数已达上限。搜索次数将于每日零点自动重置，届时欢迎您继续使用。`, `联网次数用尽`, {
      //     dangerouslyUseHTMLString: true,
      //     // showClose: notify.canClosed === 1,
      //     showConfirmButton: false,
      //     customClass: this.disabledTrigger ? 'alert-message-mobile-box' : '',
      //     callback: () => {
      //     }
      //   })
      //   return false
      // } else
      if (event === 'flowNodeStatus') {
        aiMessage.status = parsedData?.status
      } else if (event === 'fastAnswer') {
        const reference = parsedData?.choices[0]?.delta.content || ''
        this.$set(aiMessage.value[2].text, 'content', reference)
        // 回答过程
      } else if (event === 'answer' && parsedData) {
        // 正文
        const resText = parsedData?.choices[0]?.delta.content || ''
        const newContent = aiMessage.value[1].text.content + resText
        //           (parsedData?.choices[0]?.delta.content || "");
        // 思考过程
        const reasoning =
          aiMessage.value[0].reasoning.content +
          (parsedData?.choices[0]?.delta.reasoning_content || '')
        this.$set(aiMessage.value[0].reasoning, 'content', reasoning)
        this.$set(aiMessage.value[1].text, 'content', newContent)
        //         console.log("aiMessage", aiMessage, "内容", resText);
        if (this.funTitle === '文档阅读' && resText && !this.disabledTrigger) {
          this.speechList.push(resText)
          if (!this.startReading && this.showHelpRead) {
            console.log('开始播放', this.speechList)
            this.startReading = true
            setTimeout(() => {
              this.startReading = false
              this.playAudio()
            }, 1500)
          }
          //           window?.speechSynthesis?.cancel();
        }
        if (parsedData?.choices[0]?.finish_reason === 'stop') {
          this.isThinking = false
          aiMessage.status = ''
          // 判断不是excel模式的时候才清空funTitle
          if (!this.isShowExcelBox) {
            this.funTitle = ''
          }

          if (this.disabledTrigger) {
            this.getChatMsgList()
            this.getHistoryList()
          } else {
            getHistories({
              offset: 0,
              pageSize: this.pageSize,
              appId: appId,
              source: 'online'
            }).then((res) => {
              this.historyList = [...res.data.list]
              const currentChat = this.historyList.find(
                (item) => item.chatId === this.curChat.chatId
              )
              this.curChat = currentChat || this.curChat
              this.historyItemClick(this.curChat)
              // this.curChat = this.historyList[0]
            })
          }
        }
      } else if (event === 'flowResponses') {
        aiMessage.status = ''
      } else {
        console.log('event', event)
      }
      // 只有当autoScroll为true时才自动滚动到底部
      if (this.autoScroll) {
        this.scrollToBottom('usesse 358')
      }
    }
  }
}
