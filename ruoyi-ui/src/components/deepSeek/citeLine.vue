<template>
  <el-popover
    placement="top"
    width="500"
    trigger="hover"
    :disabled="!isValidId"
    @show="handlePopoverShow"
  >
    <!-- 触发器 -->
    <el-button
      slot="reference"
      type="text"
      :disabled="!isValidId"
      class="cite-link-button"
    >
      <i class="el-icon-link cite-icon" />
    </el-button>

    <!-- 弹框内容 -->
    <div v-loading="loading" class="cite-popover-content">
      <div v-if="!loading && datasetCiteData" class="cite-content">
        <!-- 头部信息 -->
        <div class="cite-header">
          <div class="source-info">
            <i :class="sourceData.icon" class="source-icon" />
            <span class="source-name">{{ sourceData.sourceName }}</span>
          </div>
          <el-button
            type="text"
            size="mini"
            class="view-all-btn"
            @click="handleOpenCiteModal"
          >
            查看全部引用
          </el-button>
        </div>

        <!-- 引用内容 -->
        <div class="cite-body">
          <markdown-renderer :source="datasetCiteData.q" />
          <markdown-renderer
            v-if="datasetCiteData.a"
            :source="datasetCiteData.a"
          />
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script>
// import { getQuoteData } from '@/api/dataset'
import MarkdownRenderer from './MarkdownRenderer.vue'

export default {
  name: 'CiteLink',
  components: {
    MarkdownRenderer
  },
  props: {
    id: {
      type: String,
      required: true
    },
    chatAuthData: {
      type: Object,
      default: () => ({})
    },
    showAnimation: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      datasetCiteData: null,
      hasLoaded: false
    }
  },
  computed: {
    isValidId() {
      // 验证是否为有效的ObjectId格式 (24位十六进制字符)
      return /^[a-f\d]{24}$/i.test(this.id)
    },
    sourceData() {
      if (!this.datasetCiteData?.collection) {
        return { icon: '', sourceName: '' }
      }
      return this.getCollectionSourceData(this.datasetCiteData.collection)
    }
  },
  methods: {
    // 悬停时触发数据加载
    async handlePopoverShow() {
      if (!this.isValidId || this.hasLoaded) return

      await this.getQuoteDataById(this.id)
    },

    // 获取引用数据 - 核心逻辑
    async getQuoteDataById(id) {
      if (this.loading) return

      try {
        this.loading = true

        // 调用API获取引用数据
        // const response = await getQuoteData({
        //   id,
        //   ...this.chatAuthData
        // })

        // this.datasetCiteData = response
        this.hasLoaded = true
      } catch (error) {
        console.error('获取引用数据失败:', error)
        this.$message.error('获取引用数据失败')
      } finally {
        this.loading = false
      }
    },

    // 打开引用模态框
    handleOpenCiteModal() {
      if (!this.datasetCiteData) return

      const modalData = {
        quoteId: this.id,
        sourceId: this.sourceData.sourceId,
        sourceName: this.sourceData.sourceName,
        datasetId: this.datasetCiteData.collection.datasetId,
        collectionId: this.datasetCiteData.collection._id
      }

      // 触发父组件事件
      this.$emit('open-cite-modal', modalData)

      // 或者使用全局事件总线
      this.$eventBus.$emit('openCiteModal', modalData)
    },

    // 获取数据源信息
    getCollectionSourceData(collection) {
      // 根据collection数据解析源信息
      const sourceId = collection.sourceId || ''
      const sourceName = collection.sourceName || collection.name || ''
      const icon = this.getSourceNameIcon({ sourceId, sourceName })

      return {
        sourceId,
        sourceName,
        icon
      }
    },

    // 获取源图标
    getSourceNameIcon({ sourceId, sourceName }) {
      // 根据源类型返回对应图标
      if (sourceId.includes('http')) return 'el-icon-link'
      if (sourceName.includes('.pdf')) return 'el-icon-document'
      if (sourceName.includes('.doc')) return 'el-icon-document'
      return 'el-icon-files'
    }
  }
}
</script>

<style scoped>
.cite-link-button {
  padding: 0;
  margin: 0;
  min-height: auto;
  line-height: 1;
}

.cite-icon {
  color: #7d09f1;
  font-size: 16px;
  cursor: pointer;
}

.cite-popover-content {
  max-height: 300px;
  overflow-y: auto;
}

.cite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f4f6f8;
}

.source-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.source-icon {
  margin-right: 4px;
  font-size: 12px;
}

.source-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.view-all-btn {
  color: #7d09f1;
  font-size: 12px;
}

.cite-body {
  font-size: 14px;
  line-height: 1.6;
}
</style>
