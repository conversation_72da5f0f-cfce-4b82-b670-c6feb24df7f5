<template>
  <transition name="fade">
    <div class="comMsgLeft">
      <div class="buttonEa">
        <div class="padding-2 margin-right-5 avatar-img flex">
          <!-- 左侧的头像 -->
          <svg-icon icon-class="zd1" style="font-size: 32px" />
          <div style="margin-left: 8px; margin-bottom: 5px">
            <el-tag
              v-if="['running', 'thinking'].includes(info.status)"
              class="margin-left-12"
              type="success"
            >
              <span
                v-show="info.status === 'running' || info.status === 'thinking'"
                class="spinner"
                style="left: -16px; top: 2px"
                :class="{ blinking: info.status === 'running' }"
              >
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
                <span class="spinner-bubble" style="background-color: #13ce66" />
              </span>
              AI 对话
            </el-tag>
            <el-tag
              v-else-if="['error'].includes(info.status)"
              class="margin-left-12"
              type="warning"
            >AI 链接失败
            </el-tag>
          </div>
        </div>

        <!-- <div class="time">
        {{ new Date().getHours() + ':' + new Date().getMinutes() }}
      </div> -->
        <div
          class="radius ai-msg-container"
          :class="{
            'thinking-text': ['running', 'thinking'].includes(info.status)
          }"
          :style="{
            'max-width': deviceType === 'mobile' ? '100%' : '80%'
          }"
        >
          <div
            v-if="internetInfo && internetInfo.length"
            class="internet-info pointer"
            @click="handleInternet"
          >
            <span>已搜索到{{ internetInfo.length }}个网页</span>
            <i class="el-icon-arrow-right" />
          </div>
          <!--      思考过程-->
          <!-- <el-collapse v-if="compiledMarkdownReasoning" v-model="activeName" accordion>
            <el-collapse-item title="思考过程" name="1"> -->
          <!-- <template slot="title">
                <div>
                  <el-button
                    style="color: #111824; border-color: #e8ebf0"
                    plain
                    @click.stop="activeName = activeName == 1 ? '0' : '1'"
                  >
                    <svg-icon icon-class="think" />
                    思考过程
                    <i
                      :class="{ roll: activeName == '0' }"
                      class="el-icon-arrow-down rollAnimation"
                    />
                  </el-button>
                </div>
              </template> -->
          <!-- v-html="compiledMarkdownReasoning" -->
          <div
            v-if="compiledMarkdownReasoning"
            class="think"
            :class="activeName == '1' ? 'think-close' : ''"
          >
            <div
              class="reasoning"
              @click="activeName == '0' ? (activeName = '1') : (activeName = '0')"
            >
              <div v-if="isThinkingRendered" class="hight-think">深度思考中...</div>
              <div v-else>已完成思考</div>
              <div class="svg">
                <svg-icon :icon-class="activeName == '1' ? 'think-open' : 'think-close'" />
              </div>
            </div>
            <div
              class="padding-l-10 reasoning-answer"
              v-html="DOMPurify.sanitize(compiledMarkdownReasoning)"
            />
          </div>
          <!-- </el-collapse-item>
          </el-collapse> -->
          <!--      回复内容-->
          <div class="markdown-container" v-html="DOMPurify.sanitize(compiledMarkdown)" />
          <div class="text" style="display: flex; align-items: center; gap: 8px">
            <span
              v-show="info.status === 'running' && isSpinner"
              class="spinner"
              :class="{ blinking: info.status === 'running' }"
            >
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
              <span class="spinner-bubble" />
            </span>
            <div
              v-if="isLoadingAnswer && ['running', 'thinking'].includes(info.status)"
              class="spinner-text"
            >
              你的答案正在加载中...
            </div>
          </div>

          <!-- 引用文件 -->
          <!-- 根据fastgpt目前的逻辑 节点给的是片段信息 所以要去重重复的文件名-->
          <div
            v-if="
              !['running', 'thinking'].includes(info.status) &&
                info.totalQuoteList &&
                info.totalQuoteList.length
            "
            class="quote-container"
          >
            <div class="quote-label">引用文件:</div>
            <div class="quote-value">
              <div v-for="(item, i) of totalQuoteList" :key="i" @click="handleReadPower(item)">
                {{ formatFilenameWithTimestamp(item.sourceName) }}
              </div>
            </div>
          </div>
          <!--详情按钮-->
          <div v-if="info.status != 'running' && info.llmModuleAccount > 0">
            <!-- <el-tag class="margin-right-5 pointer" type="success" @click="historyPreviewClick"> -->
            <!-- <el-tag class="margin-right-5 pointer" type="success">
          {{ info.historyPreviewLength }}条上下文
        </el-tag> -->
            <el-tag
              class="margin-right-5"
              style="color: #a558c9; border-color: rgb(238, 204, 255); background-color: white"
            >
              {{ info.totalRunningTime || 1 }}s
            </el-tag>
            <!-- <el-button size="mini">查看详情</el-button> -->
          </div>
          <div
            v-if="
              [undefined].includes(info.status) &&
                !rawMarkdown &&
                !info.totalQuoteList.length &&
                !reasoning
            "
            class="ai-error"
          >
            很抱歉，我们遇到一点问题,请您重新发送
          </div>
          <div
            v-if="!['running', 'thinking', 'error'].includes(info.status)"
            class="operate-btns flex flex-between"
            style="flex-wrap: wrap"
          >
            <div class="flex mt-16" style="flex-wrap: wrap">
              <el-tooltip
                :disabled="disabledTrigger"
                effect="dark"
                :content="
                  readStatus === 'stop'
                    ? '朗读'
                    : readStatus === 'pendding'
                      ? '合成中...'
                      : '停止朗读'
                "
              >
                <span
                  v-if="readStatus === 'stop'"
                  type="text"
                  class="pointer"
                  style="white-space: nowrap"
                  @click="readClick"
                >
                  <svg-icon icon-class="read-content1" class="margin-right-4 icon-btn icon-btn1" />
                  <!-- <svg-icon icon-class="copy" class="margin-right-4" /> -->
                  <!-- <span style="font-size: 12px;color:#999999">朗读</span> -->
                </span>
                <span
                  v-else-if="readStatus === 'pendding'"
                  class="icon-text margin-right-8 el-icon-loading"
                  @click="stopProductAudio"
                />
                <span
                  v-else-if="readStatus === 'reading'"
                  class="margin-right-8 flex pointer"
                  @click="stopClick"
                >
                  <!-- <svg-icon icon-class="stop-record" />
                  <img
                    style="height: 24px"
                    src="../../assets/images/speaking.gif"
                  > -->
                  <audio-wave :num="4" />
                </span>
              </el-tooltip>
              <div>
                <select-speed v-if="isReading" v-model="preSoundSpeed" @change="speedChange" />
              </div>
              <el-tooltip
                effect="dark"
                :disabled="disabledTrigger"
                :content="!downLoading ? '语音下载' : '下载中...'"
              >
                <el-button type="text" style="padding: 0" class="margin-right-4">
                  <div v-if="!downLoading" @click="debouncedDownloadWav()">
                    <svg-icon
                      icon-class="answer-wav-download3"
                      class="margin-right-4 icon-btn icon-btn1"
                    />
                    <!-- <span style="font-size: 12px;color:#999999">{{!downLoading ? '语音下载' : '下载中...'}}</span> -->
                  </div>

                  <!-- <span v-else class=" margin-right-8 icon-text icon-download">
                    <i class="el-icon-bottom" />
                  </span> -->
                  <downloading v-else class="margin-right-8" @click.native="handleChangeDownload" />
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" content="复制" :disabled="disabledTrigger">
                <!-- <a class="focus-btn icon-btn icon-btn1" @click="writeToClipboard(rawMarkdown)">
                  <svg-icon icon-class="copy1" class="margin-right-4" />
                </a> -->
                <a class="focus-btn icon-btn icon-btn1 pointer">
                  <div
                    v-clipboard="rawMarkdown.trim()"
                    v-clipboard:success="clipboardSuccessHandle"
                    class="focus-btn"
                    style="display: inline-block"
                  >
                    <svg-icon icon-class="copy1" class="margin-right-4" />
                  </div>
                </a>
              </el-tooltip>
              <el-tooltip effect="dark" content="点赞" :disabled="disabledTrigger">
                <a
                  class="focus-btn icon-btn icon-btn1 pointer"
                  :class="{
                    'active-icon': info.userGoodFeedback
                  }"
                  @click="likeClick(true)"
                >
                  <svg-icon icon-class="like1" class="margin-right-4" />
                  <!-- <span  style="font-size: 12px;">点赞</span> -->
                </a>
              </el-tooltip>
              <el-tooltip effect="dark" content="点踩" :disabled="disabledTrigger">
                <a
                  class="focus-btn icon-btn icon-btn1 pointer margin-right-8"
                  :class="{
                    'active-icon': info.userBadFeedback
                  }"
                  @click="unlikeClick(false)"
                >
                  <svg-icon icon-class="unlike1" class="margin-right-4" />
                  <!-- <span  style="font-size: 12px;">点踩</span> -->
                </a>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div v-show="info.status === 'error'" class="error-text">信息加载失败</div>
      <!--    markdown-->
      <history-preview-dialog
        ref="historyPreviewDialog"
        :info="info"
        :chat-id="chatId"
        :app-id="appId"
      />
      <com-bad-dialog
        ref="comBadDialog"
        :info="info"
        :chat-id="chatId"
        @sendComBadDialog="sendComBadDialog"
      />
      <!-- 引入文件弹窗 -->
      <quote-file v-if="show" :value.sync="show" :name="quoteName" :list="quoteList" />
      <!-- 查看联网搜索文件弹窗 -->
      <internet-file v-if="showInternet" :value.sync="showInternet" :list="internetInfo" />
    </div>
  </transition>
</template>
<script>
import selectSpeed from './select-speed.vue'
import MarkdownIt from 'markdown-it'
import MarkdownItAbbr from 'markdown-it-abbr'
import MarkdownItAnchor from 'markdown-it-anchor'
import MarkdownItFootnote from 'markdown-it-footnote'
// import MarkdownItHighlightjs from 'markdown-it-highlightjs'
import MarkdownItSub from 'markdown-it-sub'
import MarkdownItSup from 'markdown-it-sup'
import MarkdownItTasklists from 'markdown-it-task-lists'
import MarkdownItTOC from 'markdown-it-toc-done-right'
// import MarkdownPdf from 'markdown-pdf'
// 引入DOMPurify库过滤恶意代码 适用于v-html场景 防止xss攻击
import DOMPurify from 'dompurify'

import downloading from './downloading.vue'
import internetFile from './internet-file.vue'
import { eventBus } from '@/utils/eventBus'
// 代码块高亮样式
import 'highlight.js/styles/default.css'
// 引入核心库及所需语言模块
import hljs from 'highlight.js/lib'
import javascript from 'highlight.js/lib/languages/javascript'
import ClipboardJS from 'clipboard'
import xml from 'highlight.js/lib/languages/xml'
// import 'highlight.js/styles/dark.css'
// import 'highlight.js/styles/docco.css'
// import 'highlight.js/styles/far.css'
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('xml', xml)

import {
  updateUserFeedback,
  downloadWavFile,
  getMobileFile,
  getReadFilePower,
  lookFile,
  getQuoteData
} from '@/api/deepSeekApi'
import HistoryPreviewDialog from '@/components/deepSeek/historyPreviewDialog.vue'
import ComBadDialog from '@/components/deepSeek/comBadDialog.vue'
import quoteFile from './quote-file.vue'
import CiteLink from './citeLine.vue'
import dayjs from 'dayjs'
import AudioWave from './audio-wave.vue'
import { debounce } from '@/utils/index' // 导入防抖函数
import TextMsgClass from '@/views/TextMsgClass'
import emojiRegex from 'emoji-regex'

// console.log('hljs', hljs)
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function(str, lang) {
    // 更安全的 HTML 转义函数
    const escapeForAttribute = (text) => {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
    }

    // 纯文本版本（用于复制）
    const plainText = str.replace(/<\/?[^>]+(>|$)/g, '')
    // 复制按钮 SVG（保持原有样式）
    const copyIconSvg = `<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.01" x="0.260254" y="0.76416" width="16" height="16" fill="#D8D8D8"/>
<path d="M10.8071 4.97412C11.3832 4.97424 11.8498 5.44207 11.8501 6.01807V13.6284C11.8499 14.2045 11.3832 14.6713 10.8071 14.6714H3.45361C2.87745 14.6714 2.40988 14.2045 2.40967 13.6284V6.01807C2.40993 5.442 2.87748 4.97412 3.45361 4.97412H10.8071ZM3.45361 13.6284H10.8071V6.01807H3.45361V13.6284ZM12.4214 2.85596C13.3508 2.8561 14.1108 3.59325 14.1108 4.51025V11.3022C14.1108 11.5902 13.8773 11.8245 13.5894 11.8247C13.3012 11.8247 13.0669 11.5904 13.0669 11.3022V4.51025C13.0669 4.1769 12.7816 3.90004 12.4214 3.8999H5.66162C5.37354 3.89983 5.14014 3.66554 5.14014 3.37744C5.14027 3.08945 5.37362 2.85603 5.66162 2.85596H12.4214Z" fill="currentColor"/>
</svg>`

    return (
      '<div class="code-block-wrapper">' +
      '  <pre class="hljs"><code>' +
      md.utils.escapeHtml(str) +
      '</code></pre>' +
      '  <button class="copy-code-btn" data-clipboard-text="' +
      escapeForAttribute(plainText) +
      '" title="复制代码">' +
      '    ' +
      copyIconSvg +
      ' 复制' +
      '  </button>' +
      '</div>'
    )
  }
})
  .use(MarkdownItAbbr)
  .use(MarkdownItAnchor)
  .use(MarkdownItFootnote)
  .use(MarkdownItSub)
  .use(MarkdownItSup)
  .use(MarkdownItTasklists)
  .use(MarkdownItTOC)

// 添加自定义规则来处理 [fileid](CITE) 标记,这是引入文件的标识
// Ruler.after(afterName, ruleName, fn[, options])
// afterName (String) -- 在此之后新增新规则。
// ruleName (String) -- 新增规则的名称。
// fn (Function) -- 规则的函数。
// options (Object) -- 规则的选项（不是强制的）
md.inline.ruler.after('emphasis', 'CITE', function CITE(state, silent) {
  var content
  var token
  var max = state.posMax
  var start = state.pos

  // 检查是否以 [ 开头
  if (state.src.charCodeAt(start) !== 0x5b /* [ */) {
    return false
  }

  // 查找 ](CITE) 模式
  if (start + 2 >= max) {
    return false
  }

  // 查找匹配的 ]
  var end = state.src.indexOf('](CITE)', start)
  if (end === -1) {
    return false
  }

  content = state.src.slice(start + 1, end)

  if (!silent) {
    token = state.push('cite_open', 'CITE', 1)

    // 检查是否包含 | 分隔符（新格式：显示文本|原始fileid）
    var displayText, originalFileid
    if (content.includes('|')) {
      var parts = content.split('|')
      displayText = parts[0]
      originalFileid = parts[1]
    } else {
      displayText = content
      originalFileid = content
    }

    // 保存显示文本和原始fileid
    token.attrs = [
      ['display', displayText],
      ['fileid', originalFileid]
    ]
    token.markup = '[*](CITE)'

    token = state.push('text', '', 0)
    token.content = displayText

    token = state.push('cite_close', 'CITE', -1)
    token.markup = '[*](CITE)'
  }

  state.pos = end + 7 // 移动到 ](CITE) 之后
  return true
})
// 自定义渲染规则
md.renderer.rules.cite_open = function(tokens, idx) {
  var token = tokens[idx]
  var originalFileid = token.attrs[1][1] // 原始fileid
  return '<cite class="file-cite" data-fileid="' + originalFileid + '"><svg class="chakra-icon css-1u3cfqg" style="display: inline-block;line-height: 1em;flex-shrink: 0;color: var(--chakra-colors-primary-700);width: 1rem;height: auto;box-sizing: content-box;vertical-align: top;fill: currentcolor;cursor: pointer;" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="128" height="128" focusable="false"><path d="M742.4 179.2H281.6a128 128 0 0 0-128 128v596.71L298.496 768H742.4a128 128 0 0 0 128-128V307.2a128 128 0 0 0-128-128zm-460.8 51.2h460.8a76.8 76.8 0 0 1 76.8 76.8V640a76.8 76.8 0 0 1-76.8 76.8H278.272L204.8 785.664V307.2a76.8 76.8 0 0 1 76.8-76.8z"></path><path d="M534.016 525.26v-.178c0-66.996 36.3-129.101 95.642-163.636a22.374 22.374 0 0 1 30.13 7.22 20.838 20.838 0 0 1-7.423 29.158 150.758 150.758 0 0 0-59.853 64.077c27.085-2.919 53.248 10.624 65.766 34.048a62.029 62.029 0 0 1-9.395 71.603 67.328 67.328 0 0 1-72.499 17.075c-25.472-9.37-42.342-32.998-42.368-59.392zm-175.616 0v-.178c0-66.996 36.3-129.101 95.642-163.636a22.374 22.374 0 0 1 30.156 7.22 20.838 20.838 0 0 1-7.45 29.158 150.758 150.758 0 0 0-59.852 64.077c27.11-2.919 53.248 10.624 65.792 34.048a62.029 62.029 0 0 1-9.42 71.603 67.328 67.328 0 0 1-72.474 17.075c-25.498-9.37-42.343-32.998-42.394-59.392z"></path></svg>'
}

md.renderer.rules.cite_close = function() {
  return '</cite>'
}

// 重写text规则，在cite标签内部时不渲染文本
md.renderer.rules.text = function(tokens, idx) {
  var token = tokens[idx]

  // 检查是否在cite标签内部
  for (var i = idx - 1; i >= 0; i--) {
    if (tokens[i].type === 'cite_open') {
      // 在cite标签内部，不渲染原始文本
      return ''
    }
    if (tokens[i].type === 'cite_close') {
      // 已经超出cite标签范围
      break
    }
  }

  // 正常渲染文本
  return token.content
}

const appId =
  process.env.NODE_ENV === 'production' ? '67a86833ee92f8ff6e2ccebe' : '67c811aa870793870db6010a'
export default {
  name: 'ComMsgLeft',
  components: {
    ComBadDialog,
    HistoryPreviewDialog,
    selectSpeed,
    quoteFile,
    AudioWave,
    downloading,
    internetFile,
    CiteLink
  },
  props: {
    info: { type: [String, Object], default: () => ({}) },
    appId: { type: String, default: '' },
    chatId: { type: String, default: '' },
    llmType: { type: Number, default: 0 }
  },
  data() {
    return {
      likeFlag: true,
      DOMPurify: DOMPurify,
      infoCopy: this.info,
      lasterformattedMsg: '',
      currentChunk: '',
      currentChunk2: '',
      messageQueue: [],
      messageQueue2: [],
      messageCharQueue: [], // 存储需要渲染的思考过程文本
      messageCharQueue2: [], // 存储需要渲染的正文文本
      isRendering: false, // 思考过程是否可渲染
      isThinkingRendered: false, // 思考过程是否已渲染完成
      isRendering2: false, // 正文是否可渲染
      contentRenderingStarted: false, // 内容是否开始渲染
      firstCharacterRendered: false, // 第一个字符是否已渲染
      lastRenderedText: '', // 新增：记录已渲染的完整文本
      timeoutId: null, // 新增用于保存 setTimeout 的 ID
      isProductInfoProcessed: true,
      newContentIndex: 0, // 记录已渲染的字符索引
      showInternet: false, // 显示联网搜索弹窗
      internetInfo: [],
      activeName: '0',
      activeFlag: true,
      rawMarkdown: '', // Markdown内容。
      reasoning: '', // 思考过程
      audioTool: '',
      soundSpeed: '1', // 语音倍速
      preSoundSpeed: '1',
      isReading: false,
      readStatus: 'stop',
      audioPlayer: null,
      deviceType: localStorage.getItem('deviceType'),
      downLoading: false,
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      show: false,
      quoteList: [],
      quoteName: '',
      totalQuoteList: [],
      clipboard: null,
      citePopover: null, // 存储引用弹框的引用
      citeHoverTimer: null, // 存储延迟隐藏的定时器
      currentHoverElement: null, // 当前悬停的cite元素
      isLoadingCiteData: false, // 是否正在加载引用数据
      isSpinner: false,
      isLoadingAnswer: true

    }
  },
  computed: {
    compiledMarkdown() {
      const cleanedText = this.stripIncompleteUnicode(this.rawMarkdown)
      const processedMarkdown = this.replaceCiteWithIndex(cleanedText)
      return md.render(processedMarkdown)
    },
    compiledMarkdownReasoning() {
      return md.render(this.reasoning)
    }
  },
  watch: {
    info: {
      handler(cur, old) {
        // console.log('info变化', 'cur:\n', cur, 'old:\n', old)
        // 如果不是create则处理数据变化
        if (!this.isProductInfoProcessed) {
          // 如果新旧数据都不是TextMsgClass实例则终止处理
          if (!(cur instanceof TextMsgClass) && !(old instanceof TextMsgClass)) {
            return
            // 如果新数据不是TextMsgClass实例,但是旧数据是TextMsgClass实例则处理旧数据
          } else if (!(cur instanceof TextMsgClass) && old instanceof TextMsgClass) {
            this.infoCopy = old
            this.productInfo()
          }
          // 如果当前信息是TextMsgClass实例，则渲染
          if (cur instanceof TextMsgClass) {
            this.infoCopy = cur
            // 重置第一个字符渲染标志，以便新消息能正确显示加载提示
            this.firstCharacterRendered = false
            this.productInfo()
          }
        }
        // this.initData()
        // const msg =
        // cur.value.map((item) => {
        //   if (item.type === 'text') {
        //     msg += item.text.content
        //   } else if (item.type == 'reasoning') {
        //     this.reasoning = item.reasoning.content
        //   }
        // })

        // this.initRawMarkdown(msg)
        // 内容更新后重新初始化 clipboard
        this.$nextTick(() => {
          if (this.clipboard) {
            this.clipboard.destroy()
          }
          this.initClipboard()
        })
      },
      deep: true,
      immediate: true
    },
    'info.totalQuoteList': {
      handler(newVal) {
        if (newVal) {
          // 根据 sourceName 去重
          const uniqueMap = new Map()
          this.totalQuoteList = newVal.filter((item) => {
            if (!uniqueMap.has(item.sourceName)) {
              uniqueMap.set(item.sourceName, true)
              return true
            }
            return false
          })
          this.totalQuoteList = newVal
          console.log('totalQuoteList', this.totalQuoteList)
        }
      },
      deep: true,
      immediate: true
    },
    compiledMarkdown() {
      this.$nextTick(() => {
        this.initClipboard()
        this.initCiteHover()
        // 确保代码块高亮，添加错误处理
        try {
          document.querySelectorAll('pre code').forEach((block) => {
            if (hljs && typeof hljs.highlightElement === 'function') {
              hljs.highlightElement(block)
            }
          })
        } catch (e) {
          console.warn('代码高亮处理失败:', e)
        }
      })
    }
  },
  created() {
    this.initData()
    const msg = this.productInfoAsync('created')
    this.initRawMarkdown(msg)
    // 创建防抖版本的下载函数
    this.debouncedDownloadWav = debounce(this.downloadWav, 500, false)
  },
  mounted() {
    this.initAudio()
    this.initClipboard()
    if (this.isMobileDevice()) {
      // 移动端使用 touchstart 事件，避免 click 事件的 300ms 延迟
      // 但为了避免与按钮本身的事件冲突，只监听非按钮区域
      window.addEventListener('touchstart', this.handleWindowClick, { passive: false })
    } else {
      // 桌面端使用 click 事件
      window.addEventListener('click', this.handleWindowClick)
    }
    // 添加对 cite 元素的点击事件监听
    this.$el.addEventListener('click', this.handleCiteClick)
  },
  beforeDestroy() {
    this.initAudio()
    if (this.clipboard) {
      this.clipboard.destroy()
    }

    if (this.isMobileDevice()) {
      window.removeEventListener('touchstart', this.handleWindowClick)
    } else {
      window.removeEventListener('click', this.handleWindowClick)
    }
    // 移除对 cite 元素的点击事件监听
    this.$el.removeEventListener('click', this.handleCiteClick)

    // 清理引用弹框和定时器
    this.hideCitePopover()

    // 移除cite元素的悬停事件监听器
    const citeElements = this.$el.querySelectorAll('cite.file-cite')
    citeElements.forEach((citeElement) => {
      citeElement.removeEventListener('mouseenter', this.handleCiteMouseEnter)
      citeElement.removeEventListener('mouseleave', this.handleCiteMouseLeave)
    })
  },
  methods: {
    /**
     * 获取staffId
     */
    getStaffId() {
      // 尝试获取正确的 staffId，如果没有则尝试 staffd（兼容历史数据）
      const staffId = localStorage.getItem('staffId') || localStorage.getItem('staffd')

      // 如果获取到了 staffd，将其迁移到正确的 staffId
      if (!localStorage.getItem('staffId') && localStorage.getItem('staffd')) {
        const staffdValue = localStorage.getItem('staffd')
        localStorage.setItem('staffId', staffdValue)
        console.log('已将 staffd 迁移到 staffId:', staffdValue)
      }

      return staffId || ''
    },

    /**
     * 初始化引用悬停功能
     */
    initCiteHover() {
      this.$nextTick(() => {
        // 查找所有的cite元素
        const citeElements = this.$el.querySelectorAll('cite.file-cite')

        citeElements.forEach((citeElement, index) => {
          // 移除之前的事件监听器（如果有的话）
          citeElement.removeEventListener('mouseenter', this.handleCiteMouseEnter)
          citeElement.removeEventListener('mouseleave', this.handleCiteMouseLeave)

          // 添加新的事件监听器
          citeElement.addEventListener('mouseenter', this.handleCiteMouseEnter)
          citeElement.addEventListener('mouseleave', this.handleCiteMouseLeave)
        })
      })
    },

    /**
     * 处理引用元素鼠标进入事件
     */
    async handleCiteMouseEnter(event) {
      // 清除之前的延迟隐藏定时器
      if (this.citeHoverTimer) {
        clearTimeout(this.citeHoverTimer)
        this.citeHoverTimer = null
      }

      const citeElement = event.target
      const fileid = citeElement.getAttribute('data-fileid')

      // 标记当前元素为悬停状态
      this.currentHoverElement = citeElement
      this.isLoadingCiteData = true

      if (fileid && this.info && this.info.dataId) {
        try {
          // 检查AI对话是否还在进行中
          const isAIRunning = ['running', 'thinking'].includes(this.info.status)

          if (isAIRunning) {
            // AI对话进行中，直接显示loading状态的弹框
            this.showAIRunningPopover(citeElement)
            this.isLoadingCiteData = false
            return
          }

          // 调用getQuoteData接口获取详细信息
          const params = {
            id: fileid,
            chatItemDataId: this.info.dataId,
            chatId: this.chatId,
            staffId: this.getStaffId()
          }

          // 显示加载状态的弹框
          console.log('显示loading弹框')
          this.showLoadingPopover(citeElement)

          console.log('开始调用getQuoteData接口')
          const response = await getQuoteData(params)
          console.log('getQuoteData接口返回:', response)

          // 检查用户是否还在悬停这个元素
          if (this.currentHoverElement !== citeElement) {
            // 用户已经移开鼠标，不显示结果
            console.log('用户已移开鼠标，不显示结果')
            this.isLoadingCiteData = false
            return
          }

          if (response && response.code === 200 && response.data) {
            console.log('接口成功，准备显示内容弹框')
            // 将collection._id存储到collectionId字段中，方便后续getCollectionQuote接口使用
            if (response.data.collection && response.data.collection._id) {
              response.data.collectionId = response.data.collection._id
            }
            // 使用接口返回的数据显示弹框
            this.showCitePopover(citeElement, response.data)
            console.log('已调用showCitePopover显示内容')
          } else {
            // 处理非200状态码的响应
            console.warn('getQuoteData接口返回非200状态:', response)
            if (response && response.code === 500) {
              console.error('getQuoteData接口返回500错误:', response.message || '服务器内部错误')
            }
            // 如果接口失败，尝试使用本地数据作为fallback
            this.showFallbackCitePopover(citeElement, fileid)
          }

          // 标记加载完成
          this.isLoadingCiteData = false
        } catch (error) {
          console.error('获取引用数据失败:', error)

          // 根据错误类型进行不同处理
          let errorType = 'default'

          if (error.response) {
            // 服务器返回了错误响应
            const status = error.response.status
            const message = error.response.data?.message || error.message

            if (status === 500) {
              console.error('getQuoteData接口500错误:', message)
              errorType = 'server_error'
            } else if (status === 404) {
              console.warn('getQuoteData接口404错误，引用数据不存在:', message)
              errorType = 'not_found'
            } else if (status === 403) {
              console.warn('getQuoteData接口403错误，权限不足:', message)
              errorType = 'permission_error'
            } else {
              console.error(`getQuoteData接口${status}错误:`, message)
              errorType = 'server_error'
            }
          } else if (error.request) {
            // 网络错误或请求超时
            console.error('getQuoteData网络请求失败:', error.message)
            errorType = 'network_error'
          } else {
            // 其他错误
            console.error('getQuoteData未知错误:', error.message)
            errorType = 'default'
          }

          // 尝试使用本地数据作为fallback
          if (this.totalQuoteList && this.totalQuoteList.length > 0) {
            this.showFallbackCitePopover(citeElement, fileid)
          } else {
            // 如果本地数据也没有，显示对应的错误信息
            this.showErrorPopover(citeElement, errorType)
          }

          // 标记加载完成
          this.isLoadingCiteData = false
        }
      }
    },

    /**
     * 处理引用元素鼠标离开事件
     */
    handleCiteMouseLeave(event) {
      // 清除当前悬停元素标记
      this.currentHoverElement = null

      // 设置延迟隐藏，避免鼠标快速移动时闪动
      this.citeHoverTimer = setTimeout(() => {
        // 如果不在加载中，则正常隐藏弹框
        if (!this.isLoadingCiteData) {
          this.hideCitePopover()
        } else {
          // 如果还在加载中，设置一个更长的延迟后强制隐藏
          // 防止因为状态异常导致弹框永远不消失
          setTimeout(() => {
            console.log('强制隐藏弹框，重置加载状态')
            this.isLoadingCiteData = false
            this.hideCitePopover()
          }, 2000) // 2秒后强制隐藏
        }
      }, 300) // 300ms延迟
    },

    /**
     * 显示AI对话进行中的弹框
     */
    showAIRunningPopover(targetElement) {
      // 创建弹框容器
      if (this.citePopover) {
        this.hideCitePopover()
      }

      const popover = document.createElement('div')
      popover.className = 'cite-popover-container'
      popover.innerHTML = `
        <div style="max-height: 300px; overflow-y: auto; position: relative; padding: 16px; text-align: center;">
          <div style="display: flex; align-items: center; justify-content: center; color: #666; font-size: 14px;">
            <i class="el-icon-loading" style="margin-right: 8px; font-size: 16px; animation: rotate 1s linear infinite;"></i>
            <span>AI对话进行中，请等待完成后查看引用</span>
          </div>
        </div>
      `

      // 设置弹框样式
      popover.style.cssText = `
        position: absolute;
        z-index: 9999;
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        max-width: 300px;
        font-size: 14px;
        line-height: 1.6;
      `

      // 计算弹框位置
      const rect = targetElement.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      popover.style.top = (rect.bottom + scrollTop + 10) + 'px'
      popover.style.left = (rect.left + scrollLeft) + 'px'

      // 添加到页面
      document.body.appendChild(popover)
      this.citePopover = popover
    },

    /**
     * 显示加载状态弹框
     */
    showLoadingPopover(targetElement) {
      // 创建弹框容器
      if (this.citePopover) {
        this.hideCitePopover()
      }

      const popover = document.createElement('div')
      popover.className = 'cite-popover-container'
      popover.innerHTML = `
        <div class="cite-popover-content">
          <div class="cite-loading">
            <i class="el-icon-loading"></i>
            <span style="margin-left: 8px;">加载中...</span>
          </div>
        </div>
      `

      // 设置弹框样式
      popover.style.cssText = `
        position: absolute;
        z-index: 9999;
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        padding: 12px;
        max-width: 200px;
        font-size: 14px;
        line-height: 1.6;
        text-align: center;
      `

      // 计算弹框位置
      const rect = targetElement.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      popover.style.top = (rect.bottom + scrollTop + 10) + 'px'
      popover.style.left = (rect.left + scrollLeft) + 'px'

      // 添加到页面
      document.body.appendChild(popover)
      this.citePopover = popover
    },

    /**
     * 使用本地数据作为fallback显示弹框
     */
    showFallbackCitePopover(targetElement, fileid) {
      console.log('尝试使用本地数据fallback, fileid:', fileid, 'totalQuoteList长度:', this.totalQuoteList?.length)

      if (this.totalQuoteList && this.totalQuoteList.length > 0) {
        // 查找对应的引用数据
        let citedItem = null

        // 首先尝试直接通过 fileid 查找
        citedItem = this.totalQuoteList.find((item) => item.id === fileid)
        console.log('通过id查找结果:', citedItem)

        // 如果没找到，尝试将 fileid 解释为索引
        if (!citedItem) {
          const index = parseInt(fileid, 10) - 1
          console.log('尝试索引查找, index:', index)
          if (!isNaN(index) && index >= 0 && index < this.totalQuoteList.length) {
            citedItem = this.totalQuoteList[index]
            console.log('通过索引查找结果:', citedItem)
          }
        }

        if (citedItem) {
          console.log('找到本地数据，显示弹框')
          this.showCitePopover(targetElement, citedItem)
        } else {
          console.log('本地数据中未找到对应项，显示错误信息')
          this.showErrorPopover(targetElement)
        }
      } else {
        console.log('没有本地数据，显示错误信息')
        this.showErrorPopover(targetElement)
      }
    },

    /**
     * 显示错误信息弹框
     */
    showErrorPopover(targetElement, errorType = 'default') {
      // 创建弹框容器
      if (this.citePopover) {
        this.hideCitePopover()
      }

      // 根据错误类型显示不同的提示信息
      let errorMessage = '暂无引用信息'
      let errorIcon = 'el-icon-warning'
      let errorColor = '#f56c6c'

      switch (errorType) {
        case 'server_error':
          errorMessage = '服务器暂时无法响应，请稍后重试'
          errorIcon = 'el-icon-circle-close'
          errorColor = '#f56c6c'
          break
        case 'network_error':
          errorMessage = '网络连接异常，请检查网络'
          errorIcon = 'el-icon-warning'
          errorColor = '#e6a23c'
          break
        case 'permission_error':
          errorMessage = '权限不足，无法查看引用信息'
          errorIcon = 'el-icon-lock'
          errorColor = '#f56c6c'
          break
        case 'not_found':
          errorMessage = '引用信息不存在'
          errorIcon = 'el-icon-question'
          errorColor = '#909399'
          break
        default:
          errorMessage = '暂无引用信息'
          errorIcon = 'el-icon-warning'
          errorColor = '#f56c6c'
      }

      const popover = document.createElement('div')
      popover.className = 'cite-popover-container'
      popover.innerHTML = `
        <div class="cite-popover-content">
          <div class="cite-error">
            <i class="${errorIcon}" style="color: ${errorColor};"></i>
            <span style="margin-left: 8px;">${errorMessage}</span>
          </div>
        </div>
      `

      // 设置弹框样式
      popover.style.cssText = `
        position: absolute;
        z-index: 9999;
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        padding: 12px;
        max-width: 200px;
        font-size: 14px;
        line-height: 1.6;
        text-align: center;
      `

      // 计算弹框位置
      const rect = targetElement.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      popover.style.top = (rect.bottom + scrollTop + 10) + 'px'
      popover.style.left = (rect.left + scrollLeft) + 'px'

      // 添加到页面
      document.body.appendChild(popover)
      this.citePopover = popover

      // 2秒后自动隐藏错误弹框
      setTimeout(() => {
        this.hideCitePopover()
      }, 2000)
    },

    /**
     * 显示引用弹框
     */
    showCitePopover(targetElement, citedItem) {
      console.log('showCitePopover被调用，citedItem:', citedItem)
      // 创建弹框容器
      if (this.citePopover) {
        console.log('隐藏之前的弹框')
        this.hideCitePopover()
      }

      const popover = document.createElement('div')
      popover.className = 'cite-popover-container'

      // 安全地转义HTML内容
      const escapeHtml = (text) => {
        const div = document.createElement('div')
        div.textContent = text
        return div.innerHTML
      }

      // 使用项目中的markdown编译器渲染内容
      const renderMarkdownContent = (text) => {
        if (!text) return ''
        try {
          // 使用项目中已有的markdown实例进行编译
          return md.render(text)
        } catch (error) {
          console.error('Markdown渲染失败:', error)
          // 如果渲染失败，返回转义后的原始文本
          return escapeHtml(text).replace(/\n/g, '<br>')
        }
      }

      const questionContent = renderMarkdownContent(citedItem.q || '')
      const answerContent = citedItem.a ? renderMarkdownContent(citedItem.a) : ''

      // 获取文件名，优先使用collection.name，fallback到sourceName
      const fileName = (citedItem.collection && citedItem.collection.name)
        ? citedItem.collection.name
        : (citedItem.sourceName || '未知文件')

      // 检查AI对话是否还在进行中
      const isAIRunning = ['running', 'thinking'].includes(this.info.status)

      popover.innerHTML = `
        <div class="cite-popover-content">
          ${isAIRunning ? '<div class="cite-loading-mask"><div class="cite-loading-spinner"><i class="el-icon-loading"></i><span>AI对话进行中...</span></div></div>' : ''}
          <div class="cite-header">
            <div class="source-info">
              <i class="el-icon-files source-icon"></i>
              <span class="source-name">${escapeHtml(fileName)}</span>
              <span class="cite-full-link"
                    style="color: ${isAIRunning ? '#ccc' : '#1b388c'}; font-size: 12px; text-decoration: none; cursor: ${isAIRunning ? 'not-allowed' : 'pointer'}; margin-left: 8px; transition: color 0.2s ease;"
                    ${isAIRunning ? 'disabled' : ''}
                    title="${isAIRunning ? 'AI对话进行中，请等待完成' : '查看全部引用'}">全部引用</span>
            </div>
          </div>
          <div class="cite-body">
            <div class="cite-question">${questionContent}</div>
            ${answerContent ? `<div class="cite-answer">${answerContent}</div>` : ''}
          </div>
        </div>
      `

      // 设置弹框样式
      popover.style.cssText = `
        position: absolute;
        z-index: 9999;
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        padding: 12px;
        max-width: 500px;
        max-height: 300px;
        overflow-y: auto;
        font-size: 14px;
        line-height: 1.6;
      `

      // 计算弹框位置 - 显示在文字下方
      const rect = targetElement.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      // 默认显示在文字下方，留出10px间距
      popover.style.top = rect.bottom + scrollTop + 10 + 'px'
      popover.style.left = rect.left + scrollLeft + 'px'

      // 添加弹框的鼠标事件，防止鼠标移到弹框上时隐藏
      popover.addEventListener('mouseenter', () => {
        if (this.citeHoverTimer) {
          clearTimeout(this.citeHoverTimer)
          this.citeHoverTimer = null
        }
      })

      popover.addEventListener('mouseleave', () => {
        this.citeHoverTimer = setTimeout(() => {
          this.hideCitePopover()
        }, 300)
      })

      // 添加"全部引用"链接的点击事件
      const fullLink = popover.querySelector('.cite-full-link')
      if (fullLink) {
        fullLink.addEventListener('click', (e) => {
          e.stopPropagation()

          // 检查AI对话是否还在进行中
          if (['running', 'thinking'].includes(this.info.status)) {
            this.$message.closeAll()
            this.$message.warning({
              message: 'AI 正在对话中，请等待回复完成后再查看全部引用',
              duration: 3000
            })
            return
          }

          // 隐藏弹框
          this.hideCitePopover()

          // 直接使用citedItem数据，构造handleReadPower需要的数据结构
          if (citedItem && citedItem.collection) {
            const fileid = targetElement.getAttribute('data-fileid')
            const itemForReadPower = {
              datasetId: citedItem.collection.datasetId,
              collectionId: citedItem.collectionId, // 使用之前存储的collectionId
              id: citedItem.id,
              sourceName: citedItem.sourceName || citedItem.collection.name,
              fileid: fileid, // 添加fileid用于区分调用来源
              fromCitePopover: true // 标记来源于引用弹框
            }
            console.log('构造的引用数据:', itemForReadPower)
            this.handleReadPower(itemForReadPower)
          } else {
            console.warn('citedItem数据不完整:', citedItem)
            this.$message.warning('无法获取引用文件信息')
          }
        })
      }

      // 添加到页面
      document.body.appendChild(popover)
      this.citePopover = popover
      console.log('内容弹框已添加到DOM，弹框内容:', popover.innerHTML.substring(0, 200) + '...')

      // 调整位置避免超出视窗
      this.$nextTick(() => {
        const popoverRect = popover.getBoundingClientRect()
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight

        // 水平位置调整
        if (popoverRect.right > viewportWidth) {
          popover.style.left = rect.right + scrollLeft - popoverRect.width + 'px'
        }

        // 垂直位置调整 - 如果下方空间不够，显示在上方
        if (popoverRect.bottom > viewportHeight) {
          popover.style.top = rect.top + scrollTop - popoverRect.height - 10 + 'px'
        }

        // 如果上方也不够空间，则显示在视窗内
        if (parseInt(popover.style.top) < scrollTop) {
          popover.style.top = scrollTop + 10 + 'px'
        }
      })
    },

    /**
     * 处理全部引用按钮点击
     */
    handleFullReference(targetElement, citedItem) {
      // 获取fileid用于查找完整的引用数据
      const fileid = targetElement.getAttribute('data-fileid')

      // 尝试从totalQuoteList中找到完整的引用数据
      let fullCitedItem = null

      if (this.totalQuoteList && this.totalQuoteList.length > 0) {
        // 首先尝试直接通过 fileid 查找
        fullCitedItem = this.totalQuoteList.find((item) => item.id === fileid)

        // 如果没找到，尝试将 fileid 解释为索引
        if (!fullCitedItem) {
          const index = parseInt(fileid, 10) - 1
          if (!isNaN(index) && index >= 0 && index < this.totalQuoteList.length) {
            fullCitedItem = this.totalQuoteList[index]
          }
        }
      }

      // 如果找到了完整的数据，使用它；否则尝试使用citedItem
      const itemToUse = fullCitedItem || citedItem

      if (itemToUse && itemToUse.datasetId && itemToUse.collectionId) {
        // 调用原有的handleReadPower方法显示右侧引用文件全文
        this.handleReadPower(itemToUse)
      } else {
        console.warn('无法获取完整的引用数据:', { fileid, citedItem, fullCitedItem })
        this.$message.warning('无法获取引用文件信息')
      }
    },

    /**
     * 隐藏引用弹框
     */
    hideCitePopover() {
      // 清除延迟定时器
      if (this.citeHoverTimer) {
        clearTimeout(this.citeHoverTimer)
        this.citeHoverTimer = null
      }

      if (this.citePopover) {
        document.body.removeChild(this.citePopover)
        this.citePopover = null
      }

      // 重置相关状态，防止状态不一致
      this.currentHoverElement = null
      this.isLoadingCiteData = false
    },

    /**
     * 将原始 Markdown 中的 [fileid](CITE) 替换为 [index](CITE)
     * @param {string} markdown - 原始 Markdown 文本
     * @return {string} 处理后的 Markdown 文本
     */
    replaceCiteWithIndex(markdown) {
      if (!this.totalQuoteList || this.totalQuoteList.length === 0) {
        return markdown
      }

      // 按 sourceName 对引用进行分组
      const fileGroups = {}
      this.totalQuoteList.forEach((item, index) => {
        if (!fileGroups[item.sourceName]) {
          fileGroups[item.sourceName] = []
        }
        fileGroups[item.sourceName].push({
          index: index,
          item: item
        })
      })

      // 使用正则表达式匹配 [fileid](CITE) 格式
      return markdown.replace(/\[([^\]]+)\]\(CITE\)/g, (match, fileid) => {
        // 在 totalQuoteList 中查找匹配的索引
        const index = this.totalQuoteList.findIndex((item) => item.id === fileid)

        // 如果找到了，替换为索引号；否则保持原样
        if (index !== -1) {
          const item = this.totalQuoteList[index]
          // 查找该文件中的片段序号
          const fileGroup = fileGroups[item.sourceName]
          const fragmentIndex = fileGroup.findIndex((fragment) => fragment.index === index)

          if (fragmentIndex !== -1) {
            // 显示为 [文件名:片段序号](CITE)，但保留原始fileid
            const fileName = item.sourceName.replace(/\.[^/.]+$/, '') // 去除扩展名
            // 使用特殊格式来保存原始fileid: [显示文本|原始fileid](CITE)
            return `[${fileName}:${fragmentIndex + 1}|${fileid}](CITE)`
          } else {
            // fallback 到全局索引
            return `[${index + 1}|${fileid}](CITE)`
          }
        }
        return match
      })
    },
    /**
     * 处理引用文件标记的点击事件 - 修改为使用索引查找文件
     */
    handleCiteClick(event) {
      // 移除cite元素的点击处理，现在通过悬停弹框中的"全部引用"按钮来处理

      // 排除按钮元素，避免与 ClipboardJS 冲突
      const btn = event.target.closest('.copy-code-btn')
      if (btn) {
        // 不处理按钮点击，让 ClipboardJS 处理
        return
      }
      if (event.target.handledByComMsgLeft) {
        return
      }
    },
    /**
     * 将文件名中的时间戳转换为日期格式
     * @param {string} filename - 原始文件名
     * @return {string} 格式化后的文件名
     */
    formatFilenameWithTimestamp(filename) {
      // 匹配文件名中的时间戳模式（13位数字）
      const timestampRegex = /(.*)-(\d{13})\.(.*)/
      const match = filename.match(timestampRegex)

      if (match) {
        const name = match[1]
        const timestamp = match[2]
        const extension = match[3]

        // 将13位时间戳转换为日期
        const date = new Date(parseInt(timestamp))

        // 格式化日期为 YYYY-MM-DD
        const formattedDate =
          date.getFullYear() +
          '年' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '月' +
          String(date.getDate()).padStart(2, '0') +
          '日'

        // 返回格式化后的文件名
        return `${name}-${formattedDate}.${extension}`
      }

      // 如果不匹配时间戳模式，返回原始文件名
      return filename
    },
    isMobileDevice() {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera

      // 检查是否为鸿蒙系统
      const isHongmeng = /Hongmeng|HarmonyOS|HMOS/i.test(userAgent)

      // 检查传统的移动设备标识
      const isTraditionalMobile = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        userAgent
      )
      const isiPad =
        /iPad|iPhone OS|Macintosh|Mac OS X/i.test(userAgent) && 'ontouchend' in document

      return isHongmeng || isTraditionalMobile || isiPad
    },
    handleWindowClick(e) {
      // 排除按钮元素，避免与 ClipboardJS 冲突
      const btn = e.target.closest('.copy-code-btn')
      if (btn) {
        // 不处理按钮点击，让 ClipboardJS 处理
        return
      }
      if (e.target.handledByComMsgLeft) {
        return
      }
    },
    initClipboard() {
      // 销毁旧实例
      if (this.clipboard) {
        this.clipboard.destroy()
      }

      // 创建新实例
      this.clipboard = new ClipboardJS('.copy-code-btn')

      // 成功回调
      this.clipboard.on('success', (e) => {
        // 阻止默认的剪贴板行为，使用我们自定义的处理
        e.clearSelection()
        this.showCopySuccess(e.trigger)
      })

      // 失败回调
      this.clipboard.on('error', (e) => {
        console.error('复制失败:', e)
        this.fallbackCopy(e.text)
      })
    },

    showCopySuccess(button) {
      // 检查是否已经显示了成功提示
      if (button.classList.contains('copy-success')) {
        return
      }

      // 添加防重复点击机制
      if (button.dataset.copying === 'true') {
        return
      }

      // 标记正在复制
      button.dataset.copying = 'true'

      // 判断是否是代码块复制按钮
      const isCodeBlockCopy = button.classList.contains('copy-code-btn')

      // 显示相应的成功消息
      this.$message.closeAll()
      if (isCodeBlockCopy) {
        this.$message({
          message: '代码复制成功，特殊字符已转义',
          type: 'success',
          duration: 3000
        })
      } else {
        this.$message({
          message: '内容已复制',
          type: 'success',
          duration: 3000
        })
      }

      // 添加成功状态类
      button.classList.add('copy-success')

      // 保存原始按钮内容
      const originalContent = button.innerHTML

      // 修改按钮内容为"已复制"状态
      button.innerHTML = '<i class="el-icon-check"></i> 已复制'

      // 2秒后恢复原始内容和按钮状态
      setTimeout(() => {
        if (button.classList.contains('copy-success')) {
          button.innerHTML = originalContent
          button.classList.remove('copy-success')
        }
        // 清除复制标记
        button.dataset.copying = 'false'
      }, 2000)
    },

    fallbackCopy(text) {
      // 降级方案
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.left = '-9999px'
      document.body.appendChild(textarea)
      textarea.select()

      try {
        document.execCommand('copy')
        this.$message.success('复制成功（降级方案）')
      } catch (err) {
        this.$message.error('复制失败，请手动选择文本')
      } finally {
        document.body.removeChild(textarea)
      }
    },
    initData() {
      // this.showInternet = false// 显示联网搜索弹窗
      // this.internetInfo = []
      // this.activeName = '1'
      // this.rawMarkdown = '' // Markdown内容。
      // this.reasoning = '' // 思考过程
      // this.audioTool = ''
      // this.soundSpeed = '1' // 语音倍速
      // this.preSoundSpeed = '1'
      // this.isReading = false
      // this.readStatus = 'stop'
      // this.audioPlayer = null
      // this.downLoading = false
      // this.show = false
      // this.quoteList = []
      // this.quoteName = ''
      this.isLoadingAnswer = true
      this.firstCharacterRendered = false
    },
    stopProductAudio() {
      this.readStatus = 'stop'
    },
    handleChangeDownload() {
      console.log('为什么')
      this.downLoading = false
    },
    /** 联网搜索弹窗 */
    handleInternet() {
      this.showInternet = true
    },
    findStringDiff(str1, str2) {
      // 判断两个参数是否都为字符串类型
      if (typeof str1 !== 'string' || typeof str2 !== 'string') {
        return '' // 或者根据需求返回其他值或抛出异常
      }
      // console.log('str1', str1)
      // console.log('str2', str2)
      let diff = ''
      const maxLength = Math.max(str1.length, str2.length)
      if (str1.length > str2.length) {
        for (let i = 0; i < maxLength; i++) {
          if (str1[i] !== str2[i]) {
            diff += str1[i] || ''
          }
        }
      } else {
        for (let i = 0; i < maxLength; i++) {
          if (str1[i] !== str2[i]) {
            diff += str2[i] || ''
          }
        }
      }

      return diff
    },
    extractValidEmojis(text) {
      const regex = emojiRegex()
      let match
      const emojis = []

      while ((match = regex.exec(text)) !== null) {
        emojis.push(match[0])
      }

      return emojis
    },
    stripIncompleteUnicode(str) {
      return str.replace(/[\ud800-\udbff]$/g, '') // 删除结尾处未闭合的 high surrogate
    },
    splitByWordsOrEmoji(text) {
      const words = []
      let buffer = ''
      let i = 0

      while (i < text.length) {
        const char = text[i]

        // 判断是否是 Emoji（简化版）
        if (
          (char.charCodeAt(0) >= 0x1f600 && char.charCodeAt(0) <= 0x1f64f) || // Emoticons
          (char.charCodeAt(0) >= 0x1f300 && char.charCodeAt(0) <= 0x1f5ff) || // Symbols & pictographs
          (char.charCodeAt(0) >= 0x1f680 && char.charCodeAt(0) <= 0x1f6ff) || // Transport and map symbols
          (char.charCodeAt(0) >= 0x2600 && char.charCodeAt(0) <= 0x26ff)
        ) {
          // Miscellaneous symbols
          if (buffer) {
            words.push(buffer)
            buffer = ''
          }
          words.push(char)
          // 英文单词或数字
        } else if (/[a-zA-Z0-9_]/.test(char)) {
          buffer += char
          // 非英文字符（如中文、标点等）
        } else {
          if (buffer) {
            words.push(buffer)
            buffer = ''
          }
          words.push(char)
        }

        i++
      }

      if (buffer) {
        words.push(buffer)
      }

      return words
    },
    markFirstCharacterRendered() {
      if (!this.firstCharacterRendered) {
        this.firstCharacterRendered = true
        this.isLoadingAnswer = false // 隐藏加载提示
        this.isSpinner = true
        console.log('第一个字符已渲染，隐藏加载提示，时间:', new Date().toISOString())

        // 触发事件通知父组件
        this.$emit('firstCharacterRendered', {
          timestamp: new Date().toISOString(),
          dataId: this.info.dataId
        })
      }
    },
    renderNextChar2(formattedMsg) {
      // 记录正文渲染开始时间
      if (!this.contentRenderingStarted) {
        this.contentRenderingStarted = true
      }
      this.isThinkingRendered = false
      console.log(`renderNextChar2`, this.messageCharQueue2.length)

      if (this.messageCharQueue2.length === 0) {
        this.isRendering2 = false
        return
      }
      this.isRendering2 = true

      const currentMsg = this.messageCharQueue2[0] // 保持当前 msg 不变，直到渲染完成
      // 使用新方法分割为单词或 Emoji
      const tokens = this.splitByWordsOrEmoji(currentMsg)
      let index = 0

      const render = () => {
        if (index < tokens.length) {
          // 正文文本正式输出起点
          console.log('正文文本正式输出字符:', tokens[index])
          this.rawMarkdown += tokens[index]

          // 标记第一个字符已渲染
          this.markFirstCharacterRendered()

          index++
          // 使用setTimeout来控制打字机效果的速度
          setTimeout(() => {
            render() // 渲染下一个字符
          }, 10) // 正文速度不能渲染太快 不然会造成数组还没加入到渲染队列就已经渲染完提前中断渲染递归 延迟时间可以根据需要调整
        } else {
          console.log('当前正文消息渲染完毕，延迟执行下一个消息渲染')
          // 当前消息渲染完毕，继续处理队列中的下一个消息
          this.messageCharQueue2.shift() // 出队列
          this.renderNextChar2()
        }
      }

      render() // 开始渲染当前消息
    },
    renderNextChar(formattedMsg, llmType) {
      // 记录思考过程渲染开始时间
      if (!this.contentRenderingStarted) {
        this.contentRenderingStarted = true
        console.log('思考过程打字机效果开始时间:', new Date().toISOString())
        this.$emit('typingAnimationStarted', 'reasoning', new Date().toISOString())
      }
      console.log(`renderNextChar`, this.messageCharQueue.length)
      if (this.messageCharQueue.length === 0) {
        this.isRendering = false
        // R1模式下当前消息队列处理完毕，并且没有正在渲染正文的情况下 调用 renderNextChar2开始渲染正文
        if (this.messageCharQueue2.length > 0 && !this.isRendering2) {
          this.renderNextChar2()
        }
        return
      }
      // console.log('renderNextChar', this.messageCharQueue.length)
      this.isRendering = true
      const currentMsg = this.messageCharQueue[0]
      let index = 0

      const render = () => {
        if (index < currentMsg.length) {
          // 思考过程文本正式输出起点
          console.log('思考过程正式输出字符:', currentMsg[index])
          this.reasoning += currentMsg[index] // 添加当前字符到reasoning

          // 标记第一个字符已渲染
          this.markFirstCharacterRendered()

          index++
          // 使用setTimeout来控制打字机效果的速度
          this.timeoutId = setTimeout(() => {
            render() // 渲染下一个字符
          }, 10) // 延迟时间可以根据需要调整
        } else {
          console.log('当前思考过程消息渲染完毕，延迟执行下一个消息渲染')
          this.messageCharQueue.shift()
          // 当前消息渲染完毕，继续处理队列中的下一个消息
          this.renderNextChar(formattedMsg, llmType)
        }
      }

      render() // 开始渲染当前消息
    },
    processQueue2(formattedMsg, llmType) {
      const lastChar = JSON.parse(JSON.stringify(this.currentChunk2))
      // 当前的信息为
      this.currentChunk2 = JSON.parse(JSON.stringify(formattedMsg))
      this.isThinkingRendered = false
      if (this.activeFlag) {
        this.activeName = '1'
        this.activeFlag = false
      }

      const text = this.findStringDiff(this.currentChunk2, lastChar) || ''
      // console.log('%c正文渲染字段', 'color: green;', text)
      // console.log('this.rawMarkdown', this.rawMarkdown)
      // console.log('lastChar', lastChar)
      // console.log('this.currentChunk2', this.currentChunk2)
      // console.log('this.currentChunk2 === lastChar?', this.currentChunk2 === lastChar)
      if (text.length === 0 || text === '') {
        console.log('应该return了')
        return
      } else {
        // 将差值存入渲染数组
        this.messageCharQueue2.push(text)
        // 首先可以渲染
        if (!this.isRendering2) {
          // 判断模型类型 0: 默认模型
          //  0: r1 有思考过程
          //  1: v3 无思考过程
          // if (llmType === 0) {
          //   // 不直接渲染正文 而是从思考过程中接上正文渲染(为了完成正确的渲染顺序)
          //   console.log('R1模式 有思考过程 不直接渲染正文 llmType=0', llmType)
          // } else if (llmType === 1) {
          //   console.log('V3模式 没有思考过程 直接渲染正文 llmType=1', llmType)
          //   this.renderNextChar2(formattedMsg) // 如果当前没有正在渲染，则开始渲染
          // }
          this.renderNextChar2(formattedMsg)
        }
      }
    },
    processQueue(formattedMsg) {
      // const currentChunk = this.messageQueue[this.newContentIndex]
      // 记录原来的信息
      const lastChar = JSON.parse(JSON.stringify(this.currentChunk))
      // 当前的信息为
      this.currentChunk = JSON.parse(JSON.stringify(formattedMsg))
      // let nextChar = ''
      // if (this.newContentIndex > 1) {
      //   nextChar = this.messageQueue[this.newContentIndex - 1]
      // } else if (this.newContentIndex === 1) {
      //   nextChar = this.messageQueue[0]
      // } else if (this.newContentIndex === 0) {
      //   nextChar = ''
      // }
      const text = this.findStringDiff(this.currentChunk, lastChar) || ''
      console.log(this.newContentIndex, '思考过程渲染字段', text)
      // console.log('currentChunk:', this.currentChunk, 'lastChar:', lastChar)
      if (text.length === 0 || text === '') {
        console.log('应该return了')
        return
      } else {
        // 将差值存入渲染数组
        this.messageCharQueue.push(text)
        this.newContentIndex++
        if (!this.isRendering) {
          this.renderNextChar(formattedMsg) // 如果当前没有正在渲染，则开始渲染
        }
      }
    },
    MarkdownRenderNextChar(msg, llmType) {
      const formattedMsg = msg
      // .replace(/\[(.*?)\]($|\n)/g, '[$1]()').replace(/\\n/g, '\n&nbsp;')
      if (formattedMsg !== '' && msg !== this.currentChunk2) {
        this.processQueue2(formattedMsg, llmType)
        // 新增英文单词或代码块检测逻辑
        // const words = formattedMsg.split(/\b/) // 按单词边界分割
        // let buffer = ''
        // for (const word of words) {
        //   if (/^[a-zA-Z0-9_]+$/.test(word.trim()) && word.length > 1) {
        //     // 如果是英文单词或代码片段，整体渲染
        //     this.processQueue2(buffer + word)
        //     buffer = ''
        //   } else {
        //     buffer += word
        //   }
        // }
        // if (buffer) {
        //   this.processQueue2(buffer, llmType)
        // }
      } else {
        if (formattedMsg === '') {
          console.log('正文 没有进入文字动画执行 因为formattedMsg === --')
        }
        if (formattedMsg === this.currentChunk2) {
          console.log('正文 没有进入文字动画执行 因为formattedMsg === this.currentChunk2')
        }
      }
    },
    // 思考过程的文字渲染逻辑 限制传入文字的速度来限制渲染速度
    ThinkingRenderNextChar(msg) {
      const formattedMsg = msg
      if (formattedMsg !== '' && formattedMsg !== this.currentChunk) {
        this.processQueue(formattedMsg)
      }
    },
    productInfoAsync() {
      // 实现异步逻辑并返回结果
      let msg = ''
      this.info.value.forEach((item) => {
        if (
          item.type === 'text' &&
          item.text.content &&
          !item.text.content.includes('<search_func>')
        ) {
          msg = msg + item.text.content
        } else if (item.type === 'reasoning') {
          this.reasoning = item.reasoning.content
          console.log('productInfoAsync 初始化数据')
          this.isProductInfoProcessed = false
        } else if (
          item.type === 'text' &&
          item.text.content &&
          item.text.content.includes('<search_func>')
        ) {
          // console.log(`%cproductInfoAsync 带<search_func>的正文 进入渲染`, 'color:#1890ff', item.text.content)
          if (item.text.content.includes('</http_code>')) {
            // console.log(`%cproductInfoAsync 带<search_func>并且带有</http_code>的正文 进入渲染`, 'color:#1b388c', item.text.content)
            const info = item.text.content.split('</http_code>')[1]
            msg = msg + info
          }
          const reference = item.text.content.split('<search_func>')[1].split('</search_func>')[0]
          this.internetInfo = JSON.parse(reference)?.references || []
        }
      })
      return msg
    },
    productInfo(type) {
      // const msg = ''
      // console.log('infoCopy', this.infoCopy)
      this.infoCopy.value.forEach((item) => {
        // 正文
        if (
          item.type === 'text' &&
          item.text.content &&
          !item.text.content.includes('<search_func>')
        ) {
          if (this.rawMarkdown !== item.text.content) {
            // this.llmType 默认为 0
            // 0=>R1模式有思考过程
            // 1=>V3模式无思考过程
            console.log(
              'productInfo 正文 进入渲染动画',
              this.llmType === 0 ? 'R1模式有思考过程' : 'V3模式无思考过程'
            )
            // console.log('this.rawMarkdown：\n', this.rawMarkdown)
            // console.log('item.text.content：\n', item.text.content)
            // 正文需要去掉开头空格，不然最后一次渲染会出对比字符串错位的问题
            this.MarkdownRenderNextChar(item.text.content.trimStart(), this.llmType)
          }
          // 思考过程
        } else if (item.type === 'reasoning') {
          // console.log('productInfo 思考过程,item:', JSON.parse(JSON.stringify(item.reasoning.content)), 'this.reasoning:', this.reasoning)
          if (this.reasoning !== item.reasoning.content) {
            console.log('productInfo 思考过程 进入渲染动画')
            // this.reasoning = item.reasoning.content
            if (this.llmType === 0) {
              console.log('R1模式')
              this.isThinkingRendered = true
              this.ThinkingRenderNextChar(item.reasoning.content, this.llmType)
            } else if (this.llmType === 1) {
              console.log('V3模式没有思考过程')
            }
          } else {
            if (this.reasoning === item.reasoning.content) {
              console.log(
                'productInfo 思考过程 不执行渲染因为this.reasoning === item.reasoning.content'
              )
            }
            if (item.reasoning.content === this.messageQueue[this.messageQueue.length - 1]) {
              console.log(
                'productInfo 思考过程 不执行渲染因为item.reasoning.content === this.messageQueue[this.messageQueue.length - 1]'
              )
            }
          }
          // 联网搜索 带有<search_func>的正文 这个数据是联网搜索页面的20个数据
        } else if (
          item.type === 'text' &&
          item.text.content &&
          item.text.content.includes('<search_func>')
        ) {
          // console.log(`%cproductInfo 带<search_func>的正文 进入渲染`, 'color:#1890ff', item.text.content)
          if (item.text.content.includes('</http_code>')) {
            // console.log(`%cproductInfo 带<search_func>并且带有</http_code>的正文 进入渲染`, 'color:#1890ff', item.text.content.split('</http_code>')[1])
            // info这个数据经常是空的可能没用
            // const info = item.text.content.split('</http_code>')[1]
            // msg = msg + info
          }
          // 联网搜索页面的20个数据 赋值
          const reference = item.text.content.split('<search_func>')[1].split('</search_func>')[0]
          this.internetInfo = JSON.parse(reference)?.references || []
          // console.log('item.text.content', item.text.content)
          // console.log('reference', reference)
          // console.log('this.internetInfo', this.internetInfo)
        }
      })
      // return msg
    },
    /** 初始化语音 */
    initAudio() {
      if (this.disabledTrigger) {
        if (this.audioPlayer) {
          this.audioPlayer.pause()
          this.audioPlayer = null
        }
      } else {
        if (window?.speechSynthesis?.speaking) {
          window?.speechSynthesis?.cancel() // 停止当前播放
        }
      }
    },
    /** 点击引用的文件 */
    async handleReadPower(item) {
      console.log(item, this.info)
      const res = await getReadFilePower({
        id: item.datasetId
      })
      if (res && res.code === 200 && res?.data?.permission && res?.data?.permission?.hasReadPer) {
        // 根据调用来源决定使用哪个ID作为initialId
        let initialId
        if (item.fromCitePopover && item.fileid) {
          // 来自引用弹框的"全部引用"按钮，使用fileid
          initialId = item.fileid
          console.log('使用fileid作为initialId:', initialId)
        } else {
          // 来自引用文件列表的点击，使用item.id
          initialId = item.id
          console.log('使用item.id作为initialId:', initialId)
        }

        const params = {
          chatId: this.chatId,
          appId,
          collectionId: item.collectionId,
          initialId: initialId,
          chatItemDataId: this.info.dataId,
          initialIndex: 1,
          pageSize: 150,
          staffId: this.getStaffId()
        }
        const res1 = await lookFile(params)
        if (res1 && res1.code === 200) {
          this.show = true
          this.quoteList = res1.data.list
          this.quoteName = item.sourceName
        }
        // console.log(res1, '文件')
      } else {
        // this.$message.error('没有权限查看')
        this.$message.warning('没有权限查看')
      }
      // console.log(res, '是否权限')
    },
    copyClick() {
      // this.writeToClipboard(this.rawMarkdown)
    },
    clipboardSuccessHandle() {
      this.$message.closeAll()
      this.$message.success({ message: '复制成功', duration: 3000 })
    },
    async writeToClipboard(text) {
      // 判断AI是否在思考 或 内容为空
      if (
        ['running', 'thinking'].includes(this.info.status) ||
        !this.rawMarkdown ||
        this.rawMarkdown.trim() === ''
      ) {
        await navigator.clipboard.writeText('')
        this.$message.success('复制成功')
        return
      }
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success({ message: '内容已复制', duration: 1000 })
      } catch (err) {
        console.error('无法写入剪切板:', err)
        // this.$message.error('无法写入剪切板')
        this.$message.warning('无法写入剪切板')
      }
    },
    likeClickDebounce() {
      let userGoodFeedback = 'yes'
      if (this.info.userGoodFeedback) {
        userGoodFeedback = ''
        this.$set(this.info, 'userGoodFeedback', '')
      } else {
        userGoodFeedback = 'yes'
        this.$set(this.info, 'userGoodFeedback', 'yes')
      }
      updateUserFeedback({
        appId: this.appId,
        chatId: this.chatId,
        dataId: this.info.dataId,
        userGoodFeedback: userGoodFeedback
      }).then((res) => {
        // this.$emit('sendComMsgLeft')
        this.$set(this.info, 'userBadFeedback', '')
        // console.log(this.info, '点赞')
      })
      setTimeout(() => {
        eventBus.$emit('refreshPoints')
      }, 200)
    },
    likeClick() {
      // 添加节流 1000ms 内只能执行一次 不然会导致积分不准确
      // unlickclick 不用加节流 因为逻辑也是基于点赞的状态
      if (!this.likeFlag) {
        // 检查是否已有相同的消息提示，如果有则关闭所有消息并显示新消息
        this.$message.closeAll()
        this.$message.warning({
          message: '请勿频繁点击',
          duration: 3000
        })
        return
      }
      this.likeFlag = false
      this.likeClickDebounce()
      setTimeout(() => {
        this.likeFlag = true
      }, 1000)
    },
    unlikeClick() {
      if (!this.info.userBadFeedback) {
        this.$refs.comBadDialog.show()
      }
    },
    sendComBadDialog() {
      // this.$emit('sendComMsgLeft')
      this.$set(this.info, 'userGoodFeedback', '')
    },
    historyPreviewClick() {
      this.$refs.historyPreviewDialog.show()
    },
    stopClick() {
      window?.speechSynthesis?.cancel()
      this.isReading = false
      this.readStatus = 'stop'
      if (this.audioPlayer) {
        this.audioPlayer.pause()
        this.readStatus = 'stop'
        // this.audioPlayer = null
      }

      this.$emit('stopReading')
    },
    speedChange(speed) {
      this.$confirm('切换倍速后将重头播放,确定要切换吗?', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        customClass: 'speedchange-confirm-dialog'
      })
        .then(() => {
          if (speed) {
            this.soundSpeed = speed
          } else {
            this.soundSpeed = this.preSoundSpeed
          }
          if (this.audioPlayer) {
            this.audioPlayer.pause()
            // this.audioPlayer = null
            this.audioPlayer.playbackRate = this.soundSpeed
            this.audioPlayer.currentTime = 0
            this.audioPlayer.play()
          }
          if (!this.disabledTrigger) {
            this.readClick()
          }
        })
        .catch(() => {
          this.preSoundSpeed = this.soundSpeed
        })
    },
    checkSpeechSupport() {
      console.log('core version:', window.SpeechSynthesisUtterance, '语音', window.speechSynthesis)
      if ('speechSynthesis' in window && 'SpeechSynthesisUtterance' in window) {
        console.log(' 支持 Web Speech API')
        return true
      } else {
        console.warn('当前环境不支持语音朗读')
        return false
      }
    },
    // 调用示例
    readClick() {
      // 检查是否有内容可朗读
      if (!this.rawMarkdown || this.rawMarkdown.trim() === '') {
        this.$message.closeAll()
        this.$message.warning({ message: '对话内容未完成，无法朗读', duration: 3000 })
        // this.$message.warning('对话内容未完成，无法朗读')
        return
      }
      const checkSpeechSupport = this.checkSpeechSupport()
      if (this.deviceType === 'mobile' || !checkSpeechSupport) {
        if (this.audioPlayer) {
          this.audioPlayer.currentTime = 0
          this.audioPlayer.play()
        } else {
          this.downloadWavFileAndRead()
        }

        return
      }

      // window speech
      window?.speechSynthesis?.cancel()
      setTimeout(() => {
        const msg = new SpeechSynthesisUtterance(this.rawMarkdown.replace(/[#]/g, ''))
        const voices = window?.speechSynthesis?.getVoices?.() || [] // 获取语言包
        const voice = voices.find((item) => {
          return item.lang === 'zh-CN'
        })
        msg.rate = this.soundSpeed
        if (voice) {
          msg.onstart = () => {
            this.isReading = true
            this.readStatus = 'reading'
            this.$emit('startReading', this.soundSpeed, this.info.dataId)
          }
          msg.onend = () => {
            this.isReading = false
            this.readStatus = 'stop'
            this.$emit('stopReading')
            msg.onstart = null
            msg.onend = null
          }
          msg.voice = voice
          window.speechSynthesis?.speak(msg)
        }
        this.audioTool = msg
      }, 1000)
    },
    async downloadWavFileAndRead() {
      this.readStatus = 'pendding'
      await downloadWavFile({
        tts_text: this.compiledMarkdown,
        prompt_wav: 'zero_shot_prompt2.wav'
      })
        .then((response) => {
          return response.arrayBuffer()
        })
        .then((buffer) => {
          if (this.readStatus === 'pendding') {
            const blob = new Blob([buffer], { type: 'audio/wav' })
            const url = URL.createObjectURL(blob)
            // 创建 Audio 对象并播放音频
            this.audioPlayer = new Audio(url)
            this.audioPlayer.playbackRate = this.soundSpeed
            this.audioPlayer.play()
            this.audioPlayer.onplaying = () => {
              console.log('音频开始播放')
              this.readStatus = 'reading'
              this.isReading = true
              this.$emit('startReading', this.soundSpeed, this.info.dataId)
            }
            this.audioPlayer.onended = () => {
              console.log('音频播放结束')
              // 在这里执行你需要的操作
              this.readStatus = 'stop'
              this.isReading = false

              this.$emit('stopReading')
            }
          }
        })
        .catch((err) => {
          console.log('ttsWav err', err)
        })
    },
    async downloadWav() {
      // 是否正在下载
      if (this.downLoading) {
        this.handleChangeDownload()
        return
      }
      // 检查 AI 回复是否已完成
      if (['running', 'thinking'].includes(this.info.status)) {
        this.$message.closeAll()
        this.$message.warning({
          message: 'AI 正在思考中，请等待回复完成后再下载语音',
          duration: 3000
        })
        // this.$message.warning('AI 正在思考中，请等待回复完成后再下载语音')
        return
      }

      // 检查是否有内容可下载
      if (!this.rawMarkdown || this.rawMarkdown.trim() === '') {
        this.$message.closeAll()
        this.$message.warning({ message: '对话内容未完成，无法下载语音', duration: 3000 })
        return
      }

      // 防止重复点击
      if (this.downLoading) {
        return
      }

      // console.log('deviceType', this.deviceType)
      // console.log('this.info', this.info)
      let wavName = ''
      // 只获取第一个文件名
      for (const item of this.info.value) {
        if (item.type === 'file') {
          wavName = item.file.name.replace(/\.[^.]+$/, '')
          break // 找到第一个文件后结束循环
        }
      }
      // console.log('wavName', wavName)
      this.downLoading = true

      if (this.deviceType === 'mobile') {
        getMobileFile({
          tts_text: this.compiledMarkdown,
          prompt_wav: 'zero_shot_prompt2.wav'
        })
          .then((res) => {
            console.log('mobile res', res)
            if (res.code === 200 && this.downLoading) {
              this.$message.success('语音下载成功')
              this.downLoading = false
              window.wx &&
                window.wx.previewFile &&
                window.wx.previewFile({
                  url: res.data.url,
                  name: `语音下载${wavName || dayjs().format('YYYYMMDDHHmmss')}.wav`,
                  size: res.data.contentLength
                })
            }
            this.downLoading = false
          })
          .catch((err) => {
            console.error('下载失败:', err)
            this.$message.warning('语音下载失败')
            this.downLoading = false
          })
        return
      } else {
        downloadWavFile({
          tts_text: this.compiledMarkdown,
          prompt_wav: 'zero_shot_prompt2.wav'
        })
          .then((response) => {
            // console.log('pc res', response)
            return response.arrayBuffer()
          })
          .then((buffer) => {
            const blob = new Blob([buffer], { type: 'audio/wav' })
            const url = URL.createObjectURL(blob)
            console.log(blob, buffer, '测试', url)
            const link = document.createElement('a')
            link.href = url
            // link.download = 'answer.wav'
            link.download = `${wavName || 'answer'}.wav`

            if (this.downLoading) {
              this.downLoading = false
              link.click()
            }
            URL.revokeObjectURL(url)
          })
          .catch((err) => {
            console.log('ttsWav err', err)
            this.downLoading = false
          })
      }
    },
    initRawMarkdown(msg) {
      // 先替换引用标记为索引号，然后再处理其他格式
      const processedMsg = this.replaceCiteWithIndex(msg)
      this.rawMarkdown = processedMsg
        .replace(/\[(.*?)\]($|\n)/g, '[$1]()')
        .replace(/\\n/g, '\n&nbsp;')
      // console.log('rawMarkdown', this.rawMarkdown)
    }
  }
}
</script>

<style lang="scss">
.comMsgLeft {
  padding: 10px 0;
  // 添加代码块复制按钮样式
  .code-block-wrapper {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    margin: 0; /* 调整外边距 */
    background-color: #f0f0f0;
    border: 1px solid #eaecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    pre {
      margin: 0;
      padding: 14px; /* 减少内边距 */
      overflow: auto;

      code {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 14px;
        line-height: 1.5;
        background: none;
        padding: 0;
      }
    }

    .copy-code-btn {
      position: absolute;
      top: 10px; /* 调整按钮位置 */
      right: 10px; /* 调整按钮位置 */
      padding: 6px 12px;
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 13px;
      cursor: pointer;
      opacity: 1;
      transition: all 0.4s ease-in-out;
      z-index: 10;
      display: flex;
      align-items: center;
      gap: 6px;
      color: #555;

      // &:hover {
      //   background-color: #fff;
      //   border-color: #999;
      //   color: #000;
      // }
      &:hover {
        background-color: #f0f0f0;
        border-color: #999;
      }
      .copy-success-tip {
        color: #28a745;
        font-weight: 500;
        margin-left: 6px;
      }
    }

    &:hover .copy-code-btn {
      opacity: 1;
    }
  }
  .buttonEa {
    .avatar-img {
      margin-bottom: 12px;
    }
  }
  .buttonEa:hover {
    .time {
      display: block;
    }
  }
  .time {
    display: none;
    font-size: 12px;
    color: #97999b;
    margin-left: 5px;
  }
  .hoverRed {
    color: #97999b;
    //&:hover {
    //  color: white;
    //}
    &Active {
      background-color: #ee9666;
      color: white;
    }
  }
  .hoverGreen {
    color: #97999b;
    //&:hover {
    //  color: white;
    //}
    &Active {
      background-color: #50b66b;
      color: white;
    }
  }
  .point {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #397e50;
    display: inline-block;
    margin-right: 3px;
    animation: blink 2s ease-in-out infinite;
  }
  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
  .markdown-body {
    position: relative;
    &::after {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 0.5em;
      background-color: #333;
      margin-left: 2px;
      animation: blink 1s infinite;
    }
    @keyframes blink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
    }
  }
  .roll {
    transform: rotateZ(-180deg);
  }
  .rollAnimation {
    vertical-align: top;
    transition: transform 0.3s linear 0s;
  }
}
.hidePopper {
  display: none;
}
.text {
  display: flex;
  align-items: center;
  gap: 8px;
}
.blink-cursor {
  display: inline-block;
  width: 1em;
  height: 1em;
  background: #666;
  margin-left: 2px;
  border-radius: 0.5em;
}

.blinking {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.el-collapse {
  border: none;
}

.el-collapse-item__arrow {
  display: none;
}

.el-collapse-item__wrap {
  border-bottom: none;
}

.el-collapse-item__content {
  padding: 6px 12px;
  // border-left: 3px solid #e5e5e5;
  border: 1px solid #ebebeb;
  border-radius: 12px;
  margin-bottom: 14px;
}
.think {
  padding: 6px 12px;
  // border-left: 3px solid #e5e5e5;
  border: 1px solid #ebebeb;
  border-radius: 12px;
  margin-bottom: 14px;
  overflow: auto;
  width: 100%; /* 或者你需要的默认宽度 */
  max-height: 3000px;
  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1), width 0.3s ease, opacity 0.3s ease;
  .reasoning-answer {
    opacity: 1;
  }
}
.think-close {
  overflow: hidden;
  width: 150px;
  max-height: 50px;
  cursor: pointer;
  .reasoning-answer {
    opacity: 0;
  }
}
.think-close:hover {
  background: rgba(0, 0, 0, 0.04);
}

.el-collapse-item__header {
  border: none;
}
.reasoning {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  .svg {
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    display: flex;
    height: 28px;
    justify-content: center;
    padding: 5px 8px;
    width: 28px;
    z-index: 10;
    cursor: pointer;
  }
}
.reasoning-answer {
  transition: 0.8s;
  line-height: 1.7;
  color: #7f7f7f;
  p {
    color: #7f7f7f;
    font-size: 14px;
  }
  p:nth-last-child(1) {
    margin-bottom: 0;
  }
  p:first-child {
    margin-top: 0;
  }
}
.speedchange-confirm-dialog {
  margin: 0 auto;
  max-width: 80%;
}
</style>
<style lang="scss" scoped>
::v-deep .file-cite {
  background: #dfdff566;
  border-radius: 9999px;
  // color:#333;
  color: #1b388c;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
  position: relative;
  text-align: center;
  vertical-align: text-bottom;
  // white-space: nowrap;
  min-width: 14px;
  font-style: normal;
}
.quote-container {
  // display: flex;
  align-items: flex-start;
  font-size: 16px;
  line-height: 32px;
  .quote-label {
    font-weight: 700;
    margin-right: 6px;
  }
  .quote-value {
    flex: 1;
    font-weight: 400;
    color: #1b388c;
    cursor: pointer;
  }
}
.ai-msg-container {
  padding: 0px 8px;
}
.msg-over {
  display: flex;
  align-items: flex-start;
}
.operate-btns {
  .icon-btn {
    font-size: 18px;
    cursor: pointer;
    font-weight: 400;
    color: #999999;
    white-space: nowrap;
  }
  .active-icon {
    color: var(--color-primary);
  }
  // .focus-btn:focus{
  //   color:var(--color-primary);
  // }
  .icon-btn1 {
    font-size: 24px;
  }
  .icon-btn2 {
    font-size: 22px;
  }
  .icon-text {
    font-size: 16px;
    cursor: pointer;
    font-weight: 400;
    color: #909090;
  }
}
.speed-container {
  .speed-list {
    li {
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    li:nth-child(-n + 3) {
      border-bottom: 1px solid #dcdcdc;
    }
  }
}
::v-deep.markdown-container > p:first-child {
  margin-top: 0;
}
.spinner {
  display: inline-block;
  position: relative;
  width: 22px;
  height: 22px;
  vertical-align: middle;
  margin-right: 8px;
}
.spinner::before {
  content: '';
  display: none;
}
.spinner::after {
  content: '';
  display: none;
}
/* 气泡圈 */
.spinner {
  /* 让伪元素失效，使用子元素气泡 */
}
.spinner-bubble {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #bbb;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  margin: -2px 0 0 -2px;
  transform-origin: 16px 0px;
  opacity: 0.8;
  animation: spinner-bubble-fade 1s linear infinite;
}
.spinner-bubble:nth-child(1) {
  transform: rotate(0deg) translate(7px, 0);
  animation-delay: 0s;
}
.spinner-bubble:nth-child(2) {
  transform: rotate(45deg) translate(7px, 0);
  animation-delay: 0.125s;
}
.spinner-bubble:nth-child(3) {
  transform: rotate(90deg) translate(7px, 0);
  animation-delay: 0.25s;
}
.spinner-bubble:nth-child(4) {
  transform: rotate(135deg) translate(7px, 0);
  animation-delay: 0.375s;
}
.spinner-bubble:nth-child(5) {
  transform: rotate(180deg) translate(7px, 0);
  animation-delay: 0.5s;
}
.spinner-bubble:nth-child(6) {
  transform: rotate(225deg) translate(7px, 0);
  animation-delay: 0.625s;
}
.spinner-bubble:nth-child(7) {
  transform: rotate(270deg) translate(7px, 0);
  animation-delay: 0.75s;
}
.spinner-bubble:nth-child(8) {
  transform: rotate(315deg) translate(7px, 0);
  animation-delay: 0.875s;
}

@keyframes spinner-bubble-fade {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.mt-16 {
  margin-top: 12px;
}
.spinner-text {
  //文字闪烁
  animation: blink 1.3s linear infinite;
  font-size: 14px;
  margin-left: 46px;
  color: #909090;
}
.error-text {
  font-size: 14px;
  margin-left: 46px;
  color: #909090;
}
</style>
<style lang="scss" scoped>
.internet-info {
  width: fit-content;
  display: inline-flex;
  padding: 6px 12px;
  align-items: center;
  background: #f5f7fa;
  border-radius: 6px;
  color: #626262;
  margin: 0 0 6px 0;
}
.thinking-text {
  // margin-left: 36px;
}
.markdown-container {
  color: #404040;
  line-height: 25px;
  word-break: break-all;
  ::v-deep p {
    margin: 0;
    line-height: 28px;
  }
  ::v-deep img {
    max-width: 100%;
  }
  ::v-deep a {
    color: #1890ff;
  }
  ::v-deep hr {
    margin: 22px 0;
  }
  ::v-deep h3 {
    margin: 6px 0;
  }
  ::v-deep ol,
  ::v-deep ul {
    padding-inline-start: 0;
    padding-left: 30px;
  }
  ::v-deep table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
  }
  ::v-deep table,
  ::v-deep th,
  ::v-deep td {
    border: 1px solid #d5d7d5;
    font-size: 14px;
    text-align: center;
    word-break: break-all;
  }
  ::v-deep td {
    padding: 4px 6px;
  }
  ::v-deep strong,
  ::v-deep h1,
  ::v-deep h2,
  ::v-deep h3,
  ::v-deep h4,
  ::v-deep h5,
  ::v-deep h6 {
    line-height: 40px;
    font-weight: 600;
  }
}
.ai-error {
  margin-top: 10px;
}

.icon-download {
  display: inline-flex;
  width: 18px;
  height: 18px;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 1px solid #909090;
  overflow: hidden;
  .el-icon-bottom {
    font-size: 12px;
    color: var(--color-primary);
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(-25%);
  }
  100% {
    transform: translateY(0);
  }
}

.hight-think {
  animation: text-show-Nn5axm 0.3s linear 0ms 1 both, text-blink-HQF0c5 2.4s ease-in infinite;
  background: linear-gradient(90deg, rgba(0, 87, 255, 0), #0057ff 50%, rgba(0, 87, 255, 0))
    no-repeat 0 0;
  -webkit-background-clip: text;
  background-clip: text;
  background-size: 50% 100%;
  color: rgba(0, 0, 0, 0.6);
  opacity: 1;
  z-index: 10;
}
@keyframes text-blink-Nn5axm {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes text-blink-HQF0c5 {
  0% {
    background-position: -100% 0;
  }
  15% {
    background-position: -100% 0;
  }
  55% {
    background-position: 200% 100%;
  }
  100% {
    background-position: 200% 100%;
  }
}

/* 引用弹框样式 */
::v-deep .cite-popover-container {
  .cite-popover-content {
    max-height: 300px;
    overflow-y: auto;
    position: relative;
  }

  .cite-loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 4px;
  }

  .cite-loading-spinner {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;

    i {
      margin-right: 8px;
      font-size: 16px;
      animation: rotate 1s linear infinite;
    }
  }

  .cite-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f4f6f8;
  }

  .source-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .source-icon {
    color: #1b388c;
    font-size: 14px;
  }

  .source-name {
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }

  .source-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;
  }

  .source-icon {
    margin-right: 4px;
    font-size: 12px;
    color: #1b388c;
  }

  .source-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
  }

  .cite-body {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }

  .cite-question {
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }

  .cite-answer {
    color: #666;
    padding-left: 12px;
    border-left: 3px solid #1b388c;
  }

  // Markdown元素样式支持
  .cite-question, .cite-answer {
    // 段落
    p {
      margin: 0.5em 0;
      &:first-child {
        margin-top: 0;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }

    // 标题
    h1, h2, h3, h4, h5, h6 {
      margin: 0.8em 0 0.4em 0;
      font-weight: 600;
      &:first-child {
        margin-top: 0;
      }
    }

    h1 { font-size: 1.2em; }
    h2 { font-size: 1.15em; }
    h3 { font-size: 1.1em; }
    h4, h5, h6 { font-size: 1em; }

    // 强调
    strong, b {
      font-weight: 600;
      color: #333;
    }

    em, i {
      font-style: italic;
    }

    // 代码
    code {
      background-color: #f5f5f5;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: 'Courier New', Courier, monospace;
      font-size: 0.9em;
      color: #e83e8c;
    }

    pre {
      background-color: #f5f5f5;
      padding: 0.8em;
      border-radius: 4px;
      overflow-x: auto;
      margin: 0.5em 0;

      code {
        background-color: transparent;
        padding: 0;
        color: inherit;
      }
    }

    // 列表
    ul, ol {
      margin: 0.5em 0;
      padding-left: 1.5em;
    }

    li {
      margin: 0.2em 0;
    }

    // 引用
    blockquote {
      border-left: 4px solid #ddd;
      padding-left: 1em;
      margin: 0.5em 0;
      color: #666;
      font-style: italic;
    }

    // 链接
    a {
      color: #1b388c;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    // 表格
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 0.5em 0;
      font-size: 0.9em;
    }

    th, td {
      border: 1px solid #ddd;
      padding: 0.4em 0.6em;
      text-align: left;
    }

    th {
      background-color: #f5f5f5;
      font-weight: 600;
    }

    // 分隔线
    hr {
      border: none;
      border-top: 1px solid #eee;
      margin: 1em 0;
    }
  }

  // Loading动画
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

/* 引用链接样式 */
::v-deep cite.file-cite {
  color: #1b388c;
  font-style: normal;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    // color: #5a07b8;
    text-decoration: none;
  }
}
</style>
<!-- animation: text-show-Nn5axm .3s linear 0ms 1 both, text-blink-HQF0c5 2.4s ease-in infinite; -->
