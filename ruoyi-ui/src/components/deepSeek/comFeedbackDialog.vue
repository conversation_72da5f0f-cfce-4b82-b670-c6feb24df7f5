<template>
  <el-dialog
    title="您的反馈对我们的提升很重要"
    :visible.sync="showDialog"
    :width="width"
    modal-append-to-body
    class="my-custom-alert"
    @close="handleDialogClose"
  >
    <div class="line" />
    <div class="title">
      反馈内容：
    </div>
    <el-input
      v-model="textarea"
      rows="8"
      height="210px"
      type="textarea"
      maxlength="500"
      show-word-limit
      placeholder="请您详细描述您的反馈内容"
    />
    <div class="title">
      上传图片（最多可上传5张）：
    </div>
    <div class="upload-container">
      <div class="image-list">
        <div v-for="(item, index) in fileList" :key="index" class="image-item">
          <div class="image-wrapper">
            <img :src="item.url" alt="上传图片" class="preview-image">
            <div class="image-overlay">
              <div class="image-actions">
                <i class="el-icon-zoom-in" @click="previewImage(item)" />
                <i class="el-icon-download" @click="downloadImage(item)" />
                <i class="el-icon-delete" @click="removeImage(index)" />
              </div>
            </div>
          </div>
        </div>
        <div v-if="fileList.length < 5" class="upload-button" @click="triggerUpload">
          <i class="el-icon-plus" />
        </div>
      </div>
      <input
        ref="fileInput"
        type="file"
        accept=".jpg,.png,.gif,.jpeg"
        multiple
        style="display: none"
        @change="handleFileInputChange"
      >
    </div>
    <div class="el-message-box__btns">
      <button type="button" class="el-button el-button--default el-button--small" @click="closeDialog">
        <span>
          取消
        </span>
      </button>
      <button type="button" class="el-button el-button--default el-button--small el-button--primary " @click="submitFeedback">
        <span>
          提交反馈
        </span>
      </button>
    </div>
    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      append-to-body
      class="preview-dialog"
    >
      <img :src="previewUrl" alt="预览图片" class="preview-image-large">
    </el-dialog>
  </el-dialog>
</template>

<script>
import CryptoJS from 'crypto-js'
import { uploadImgRequest, feedbackRequest } from '@/api/mobile'
import { eventBus } from '@/utils/eventBus'
export default {
  name: 'ComFeedbackDialog',
  props: {
    info: { type: Object },
    chatId: { type: String }
  },
  data() {
    return {
      textarea: '', // 反馈内容
      fileList: [], // 已上传图片列表
      showDialog: false,
      width: '70%',
      previewVisible: false,
      previewUrl: ''
    }
  },
  methods: {
    show() {
      this.showDialog = true
    },
    // 触发文件选择
    triggerUpload() {
      this.$refs.fileInput.click()
    },
    // 处理文件选择事件
    async handleFileInputChange(e) {
      const files = e.target.files
      if (!files || files.length === 0) return

      console.log(`选择了 ${files.length} 个文件`)

      // 检查是否超出限制
      if (this.fileList.length + files.length > 5) {
        this.$message.warning({ message: `最多只能上传 5 张图片！`, duration: 3000 })
        return
      }

      // 使用for循环顺序处理文件
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        // 限制图片大小为2M
        if (file.size > 10 * 1024 * 1024) {
          this.$message.closeAll()
          this.$message.warning({ message: `${file.name} 超过10M，无法上传！`, duration: 3000 })
          continue
        }
        console.log(`开始处理第 ${i + 1} 个文件: ${file.name}`)

        // 使用Promise包装FileReader
        const fileData = await new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            resolve(e.target.result)
          }
          reader.readAsDataURL(file)
        })

        // 生成唯一标识符：时间戳 + 随机数 + 索引
        const uniqueId = Date.now() + '_' + Math.random().toString(36).substring(2, 10) + '_' + i

        // 计算文件的MD5，加入唯一标识符确保即使相同文件也有不同的MD5
        const md5 = CryptoJS.MD5(fileData + uniqueId).toString()
        console.log(`文件 ${file.name} 的唯一MD5: ${md5}`)

        // 添加到文件列表
        this.fileList.push({
          url: fileData,
          name: file.name,
          md5: md5
        })

        // 创建一个新的FormData对象
        const formData = new FormData()
        formData.append('file', file)

        // 使用MD5作为上传参数
        await this.uploadFile(md5, formData, this.fileList.length - 1)
      }

      console.log('所有文件处理完成，文件列表:', this.fileList)

      // 清空文件输入框，允许重复选择相同的文件
      e.target.value = ''
    },

    // 实际上传方法
    async uploadFile(md5, formData, fileIndex) {
      try {
        const currentFile = this.fileList[fileIndex]
        console.log(`开始上传文件 ${currentFile.name}，MD5: ${md5}`)

        // 发送上传请求，使用MD5作为请求参数
        const response = await uploadImgRequest(md5, formData)
        console.log(`文件 ${currentFile.name} 上传响应:`, response)

        if (response && response.code === 200) {
          console.log(`文件 ${currentFile.name} 上传成功, ID: ${response.data.id}`)
          this.$message.success({ message: `${currentFile.name} 上传成功`, duration: 3000 })

          // 更新文件的ID
          this.fileList[fileIndex].id = response.data.id
          console.log(`更新文件ID成功，当前文件列表:`,
            this.fileList.map(f => ({ name: f.name, id: f.id })))
        } else {
          console.error(`文件 ${currentFile.name} 上传失败:`, response)
          this.$message.warning({ message: `${currentFile.name} 上传失败`, duration: 3000 })
        }

        return response
      } catch (error) {
        console.error(`文件上传异常:`, error)
        this.fileList.pop()
        this.$message.warning({ message: `文件上传失败`, duration: 3000 })
        throw error
      }
    },
    // 图片预览方法
    previewImage(item) {
      this.previewUrl = item.url
      this.previewVisible = true
      console.log('预览图片:', item.name)
    },

    // 下载图片方法
    downloadImage(item) {
      console.log('下载图片:', item.name)
      try {
        // 创建一个临时的a标签用于下载
        const link = document.createElement('a')
        link.href = item.url
        link.download = item.name || '图片.png' // 使用原始文件名或默认名称
        document.body.appendChild(link)
        link.click()
        // document.body.removeChild(link)
        // this.$message.success('图片正在下载中...')
      } catch (error) {
        console.error('下载图片失败:', error)
        this.$message.warning({ message: '图片下载失败', duration: 3000 })
      }
    },

    // 关闭对话框时清空内容
    closeDialog() {
      this.textarea = ''
      this.fileList = []
      this.showDialog = false
    },
    // 处理对话框关闭事件
    handleDialogClose() {
      this.textarea = ''
      this.fileList = []
    },

    // 删除图片方法
    removeImage(index) {
      console.log('删除图片, 索引:', index, '文件名:', this.fileList[index]?.name)
      this.fileList.splice(index, 1)
      this.$message.success({ message: '图片已删除', duration: 3000 })
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.previewUrl = ''
    },

    // 提交反馈
    async submitFeedback() {
      if (!this.textarea.trim()) {
        this.$message.closeAll()
        this.$message.warning({ message: '请填写反馈内容！', duration: 3000 })
        return
      }

      console.log('提交前的文件列表:', this.fileList)

      let staffId = ''
      // 如果当前为开发环境
      if (['development'].includes(process.env.VUE_APP_ENV)) {
        staffId = process.env.VUE_APP_DEV_STAFF_ID
      } else {
        // 测试和正式环境
        staffId = localStorage.getItem('staffId')
      }

      const imageIds = this.fileList.map(img => img.id).join(',')
      console.log('提交的图片ID:', imageIds)

      const data = {
        userCode: staffId,
        feedbackContent: this.textarea,
        imageIds: imageIds,
        visitFrom: localStorage.getItem('visitFrom')
      }

      console.log('提交的数据:', data)
      const res = await feedbackRequest(data)
      console.log('提交响应:', res)
      if (res.code !== 200) return this.$message.error({ message: res.msg, duration: 3000 })
      this.$message.closeAll()
      this.$message.success({ message: '提交成功', duration: 3000 })

      // 清空表单
      this.textarea = ''
      this.fileList = []
      this.showDialog = false
      this.$emit('sendComBadDialog')
      // 500ms后触发积分更新事件
      setTimeout(() => {
        eventBus.$emit('refreshPoints')
      }, 200)
    }
  }}
</script>

<style scoped lang="scss">
.line{
  height: 1px;
  background: rgba(64, 64, 64, 0.3);
  margin: 0 20px;
}
.title{
  color: #404040;
  font-weight: 400;
  font-size: 18px;
  padding: 0 20px;
  margin-top: 25px;
}
.flex{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.upload-container {
  padding: 0 20px;
  margin-top: 10px;
  margin-bottom: 20px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .image-overlay {
    opacity: 1;
  }
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-actions {
  display: flex;
  gap: 20px;

  i {
    color: white;
    font-size: 24px;
    cursor: pointer;

    &:hover {
      color: #67c23a;
    }
  }
}

.upload-button {
  width: 120px;
  height: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  i {
    font-size: 28px;
    color: #8c939d;
  }

  &:hover {
    border-color: #67c23a;

    i {
      color: #67c23a;
    }
  }
}

.preview-dialog {
  ::v-deep .el-dialog {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
  }

  ::v-deep .el-dialog__body {
    padding: 10px;
    text-align: center;
  }
}

.preview-image-large {
  max-width: 100%;
  max-height: 80vh;
}

::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  .el-dialog__header{
    text-align: center;
    font-weight: bold;
    .el-dialog__title{
      font-size: 20px;
      color: black;
    }
  }
  .el-input--medium{
    padding: 10px 20px 20px;
    textarea{
      background:#fff!important;
      border: 1px solid #DCDFE6;
    }
  }
}
::v-deep .el-textarea{
  .el-input__count{
    right: 30px;
    bottom:25px;
  }
}
::v-deep .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  padding: 0!important;
}
::v-deep .el-dialog{
  border-radius: 4px!important;
}
::v-deep .el-dialog__headerbtn{
  top:16px;
}
::v-deep .el-message-box__btns{
  height: 48px;
  padding: 0px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
::v-deep .el-button--primary {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}
</style>
