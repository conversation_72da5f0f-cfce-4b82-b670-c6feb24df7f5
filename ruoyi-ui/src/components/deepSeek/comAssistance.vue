<template>
  <el-drawer
  title="提示词帮助"
    class="comAssistance-container"
    :size="width"
    :center="true"
    :show-close="true"
    :visible.sync="showDialog"
    width="80%"
    modal-append-to-body
  >
    <!-- <div slot="title" class="tips-title">提示词帮助</div> -->
    <div class="padding-10">
      <div v-for="(v, i) in modelList" :key="i" class="tips-item">
        <div class="flex flex-between padding-bottom-10">
          <div class="title">{{ v.templateTitle }}</div>
          <el-button
            type="primary"
            plain
            size="mini"
            class="use-btn"
            @click="useClick(v)"
          >使用</el-button>
        </div>
        <div class="contentEa">
          <span
            v-for="(item,index) of v.highlightedContent"
            :key="index"
            :class="{
              'hight-light': item.highlighted,
            }"
          >{{ item.text }}</span>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getModelList } from '@/api/deepSeekApi'

export default {
  name: 'ComAssistance',
  props: {
    width: { type: String }
  },
  data() {
    return {
      showDialog: false,
      modelList: []
    }
  },
  methods: {
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {
      getModelList({ typecode: 'docassistant' }).then((res) => {
        this.modelList = res.data
      })
    },
    useClick(row) {
      this.$emit('sendComAssistance', row.highlightedContent)
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.comAssistance-container {
  .tips-title {
    font-weight: 700;
    font-size: 16px;
    color: #262626;
  }
  .tips-item {
    padding:10px;
    border-bottom: 1px solid #e5e5e5;
    margin-top: 10px;
    .title{
      font-weight: 600;
      font-size: 16px;
      color: #333;

    }
    .use-btn {
      border: none;
      border-radius: 10px;
    }
    .contentEa{
      color:#484848;
      font-weight: 400;
      font-size:16px;
      line-height:28px;
    }
  }
}
.hight-light{
  color:var(--color-primary)
}
::v-deep .el-drawer__header{
  font-weight: 600;
  color:#333;
  background-color: #fafafa;
  padding: 14px;
  margin-bottom: 0px;
}
::v-deep .el-button--primary.is-plain{
  background-color: #4CB848;
}
::v-deep .el-button--primary.is-plain span{
  color:#fff;
}
</style>
