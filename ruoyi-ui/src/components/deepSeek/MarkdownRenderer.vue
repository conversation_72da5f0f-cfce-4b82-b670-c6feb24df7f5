<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script>
import MarkdownIt from 'markdown-it'
import DOMPurify from 'dompurify'

// 创建markdown实例
const md = MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
})

export default {
  name: 'MarkdownRenderer',
  props: {
    source: {
      type: String,
      default: ''
    }
  },
  computed: {
    renderedContent() {
      if (!this.source) return ''
      
      try {
        // 渲染markdown
        const rendered = md.render(this.source)
        // 使用DOMPurify清理HTML，防止XSS攻击
        return DOMPurify.sanitize(rendered)
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        // 如果渲染失败，返回转义后的原始文本
        return this.escapeHtml(this.source)
      }
    }
  },
  methods: {
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }
  }
}
</script>

<style scoped>
.markdown-renderer {
  line-height: 1.6;
  word-wrap: break-word;
}

.markdown-renderer ::v-deep h1,
.markdown-renderer ::v-deep h2,
.markdown-renderer ::v-deep h3,
.markdown-renderer ::v-deep h4,
.markdown-renderer ::v-deep h5,
.markdown-renderer ::v-deep h6 {
  margin: 0.5em 0;
  font-weight: 600;
}

.markdown-renderer ::v-deep p {
  margin: 0.5em 0;
}

.markdown-renderer ::v-deep ul,
.markdown-renderer ::v-deep ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-renderer ::v-deep li {
  margin: 0.2em 0;
}

.markdown-renderer ::v-deep code {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9em;
}

.markdown-renderer ::v-deep pre {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.markdown-renderer ::v-deep pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-renderer ::v-deep blockquote {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin: 0.5em 0;
  color: #666;
}

.markdown-renderer ::v-deep a {
  color: #7d09f1;
  text-decoration: none;
}

.markdown-renderer ::v-deep a:hover {
  text-decoration: underline;
}

.markdown-renderer ::v-deep strong {
  font-weight: 600;
}

.markdown-renderer ::v-deep em {
  font-style: italic;
}

.markdown-renderer ::v-deep table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.markdown-renderer ::v-deep th,
.markdown-renderer ::v-deep td {
  border: 1px solid #ddd;
  padding: 0.5em;
  text-align: left;
}

.markdown-renderer ::v-deep th {
  background-color: #f5f5f5;
  font-weight: 600;
}
</style>
