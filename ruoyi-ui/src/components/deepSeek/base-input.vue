<template>
  <div ref="input" class="base-input" :contenteditable="true" :placeholder="placeholder" :data-display-placeholder="!value" @keyup.enter="handleEnter" @input="handleInput">
    <span v-show="Array.isArray(list)&&value">
      <span
        v-for="(item,i) of list"
        :key="i"
        :class="{
          'height-light':item.highlighted,
        }"
      >{{ item.text }}</span>
    </span>
    <span v-show="!Array.isArray(list)">
      {{ value }}
    </span>
  </div>
</template>
<script>
export default {
  name: 'BaseInput',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    list: {
      type: [Array, String],
      default: ''
    }
  },
  watch: {
    value(val) {
      console.log(val, this.list)
      if (!val) {
        this.$refs.input.textContent = ''
      }
    }
  },
  methods: {
    handleInput(e) {
      const value = e.target.innerText.replace(/\n/g, '')
      console.log(value)
      this.$emit('input', value)
      this.$emit('update:value', value)
    },
    handleEnter(e) {
      e.preventDefault()
      const value = e.target.innerText.replace(/\n/g, '')
      console.log(value)
      this.$emit('enter', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.base-input{
  line-height: 28px;
  width:100%;
  padding:10px;
  font-size:16px;
  font-weight:400;
  color:#262626;
  background: #f7fbf6;
}
.base-input[data-display-placeholder=true]:before{
  content: attr(placeholder);
    display: block;
    pointer-events: none;
    position: absolute;
    color: rgba(6, 7, 31, .15);
    font-size:16px;
}

.height-light{
  color:var(--color-primary)
}
</style>
