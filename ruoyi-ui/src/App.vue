<template>
  <div id="app">
    <router-view :class="[{ pc: isDesktop }, { mobile: isMobile }]" />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from '@/components/ThemePicker'
import UAParser from 'ua-parser-js'
import { getUserInfo, getUser, getAddEncryptUserInfo } from '@/api/deepSeekApi'
import Cookies from 'js-cookie'
import { setToken } from '@/utils/auth'
import { alertTest } from '@/utils/alert'

export default {
  name: 'App',
  components: { ThemePicker },
  data() {
    return {
      isMobile: false, // 是否移动端
      isTablet: false, // 是否平板
      isDesktop: false // 是否桌面端
    }
  },
  async created() {
    const url = new URLSearchParams(location.href)
    const token = url.get('access_token')
    this.detectDevice()
    if (!String(url).includes('elink')) {
      localStorage.setItem('visitFrom', 'INET')
    } else {
      if (this.isDesktop || this.isTablet) {
        // 如果是桌面或平板设备，则执行下列代码块
        localStorage.setItem('visitFrom', 'DTY')
      } else {
        localStorage.setItem('visitFrom', 'MOBILE')
      }
    }
    // if (code || ['development'].includes(process.env.VUE_APP_ENV)) {
    // 判断是否是电投宜里面
    if (String(url).includes('elink')) {
      // 这是电投宜的  因为电投宜带elink

      // 解析完整 URL
      const urlObj = new URL(location.href)
      // 获取查询参数中的 access_token
      // 添加火球不到token时的报错逻辑防止手机端一直是pc样式
      const token = urlObj.searchParams.get('access_token') || ''
      let access_token = ''
      if (token.length > 0) {
        access_token = token.split('#')[0]
      } else {
        access_token = ''
      }
      localStorage.setItem('token', access_token)
      console.log('电投宜登录')
      this.queryUser()
    } else if (token) {
      console.log('没有code,有登录token的情况')
      // 设置cookie
      const access_token = token.split('#')[0]
      Cookies.set('access_token', access_token)
      localStorage.setItem('token', access_token)
      this.queryUser()
    } else {
      console.log('现在是没有code 没有登录的情况')

      // 判断是否是  iframe 加载进来的
      if (window.self !== window.top) {
        console.log('当前页面是被嵌套在 iframe 中')
      } else {
        console.log('当前页面不是在 iframe 中')
        const baseUrl = process.env.VUE_APP_INNER_URL

        // 没有code 的情况下 要跳转 登录界面去登录
        const url = baseUrl + `/qaapi/qaassistant/oauth/login?redirect_url=${location.href}`
        console.log(url)
        location.href = url
      }
    }

    if (this.isDesktop || this.isTablet) {
      this.$router.replace({ path: `/deepseek` })
      localStorage.setItem('deviceType', 'pc')
      alertTest()
    } else {
      alertTest(true)

      localStorage.setItem('deviceType', 'mobile')
      this.$router.replace({ path: `/mobile` })
    }
  },
  methods: {
    detectDevice() {
      // 添加纯血鸿蒙系统 手机端和电脑端的额外判断
      const parser = new UAParser()
      const result = parser.getResult()
      this.isMobile =
        result.device.type === 'mobile' ||
        result.os.name === 'HM' ||
        result.os.name === 'Hongmeng OS'
      this.isTablet = result.device.type === 'tablet'
      this.isDesktop = !this.isMobile && !this.isTablet

      console.log('当前设备信息：', result, 'this.isMobile?', this.isMobile, 'this.isTablet', this.isTablet, 'this.isDesktop', this.isDesktop)
    },
    async queryUserInfo(userCode) {
      const resData = await getAddEncryptUserInfo({
        userId: userCode
      })
      console.log(resData, '想要的用户')
      if (resData && resData.code === 200) {
        localStorage.setItem('outLinkUid', resData.UserId)
        localStorage.setItem('staffId', resData.staffId)
      }
    },
    /** 电投宜登录 */
    async queryUser() {
      console.log('进入内容')
      const res = await getUser()
      if (res && res.code === 200) {
        const userCode = res?.data?.user?.userCode
        const resData = await getAddEncryptUserInfo({
          userId: userCode
        })
        if (resData && resData.code === 200) {
          localStorage.setItem('outLinkUid', resData.UserId)
          localStorage.setItem('staffId', resData.staffId)
        }
      }
    }
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
}
</script>
<style lang="scss">
#app .theme-picker {
  display: none;
}
.pc {
  .micro-icon {
    width: 20px !important;
  }
  .msgInner {
    max-width: 80%;
  }
  .msgItem1 {
    //max-width: 48%;
    max-width: calc((100vw - 600px) * 0.8 / 2);
  }
}
.mobile {
  .audioRead {
    //display: none;
  }
}
.el-popover {
  border-radius: 10px !important;
  padding: 0 10px;
}
/* 移动端适配 代码块样式*/
@media only screen and (max-width: 768px) {
  .hljs {
    white-space: break-spaces;
    word-break: break-all;
    -webkit-white-space: break-spaces;
    -moz-white-space: break-spaces;
  }
}

* {
  font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Helvetica,
    Arial, sans-serif !important;
}

.vc-switch {
  bottom: 200px;
}
.mobile-message-box {
  width: 80%;
}
</style>
