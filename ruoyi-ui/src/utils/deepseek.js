import { customAlphabet } from 'nanoid'

const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890_', 24)
export function checkOutLinkUid() {
  const uid = localStorage.getItem('outLinkUid')
  return uid
}

export function generateChatId(size) {
  const firstChar = customAlphabet('abcdefghijklmnopqrstuvwxyz', 1)()
  if (size === 1) return firstChar
  const randomsStr = customAlphabet(
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
    size - 1
  )()
  return `${firstChar}${randomsStr}`
}

/**
 * 根据文件名后缀返回对应的 MIME 类型
 * @param {string} filename - 完整文件名（如 "image.jpg"）
 * @returns {string} MIME 类型，默认返回 'application/octet-stream'
 */
export function getMimeType(filename) {
  // 处理无后缀名、隐藏文件等特殊情况
  if (!filename.includes('.') || filename.startsWith('.')) {
    return 'application/octet-stream'
  }

  // 提取后缀并转为小写
  const ext = filename.split('.').pop().toLowerCase()

  // MIME 类型映射表（精简示例，实际应包含完整列表）
  const mimeMap = {
    // 图片
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
    ico: 'image/x-icon',

    // 文档
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    odt: 'application/vnd.oasis.opendocument.text',

    // 文本
    txt: 'text/plain',
    csv: 'text/csv',
    html: 'text/html',
    htm: 'text/html',
    css: 'text/css',
    js: 'text/javascript',
    json: 'application/json',
    xml: 'application/xml',

    // 压缩文件
    zip: 'application/zip',
    rar: 'application/vnd.rar',
    '7z': 'application/x-7z-compressed',
    tar: 'application/x-tar',
    gz: 'application/gzip',

    // 音视频
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    mkv: 'video/x-matroska',

    // 编程相关
    exe: 'application/x-msdownload',
    dll: 'application/x-msdownload',
    sh: 'application/x-sh',
    php: 'application/x-httpd-php',
    py: 'text/x-python',
    java: 'text/x-java-source',

    // 其他常见类型
    ttf: 'font/ttf',
    woff: 'font/woff',
    woff2: 'font/woff2',
    eot: 'application/vnd.ms-fontobject',
    otf: 'font/otf',
    swf: 'application/x-shockwave-flash',
    apk: 'application/vnd.android.package-archive',
    ipa: 'application/octet-stream'
  }

  return mimeMap[ext] || 'application/octet-stream'
}

export function base64ToFile(base64Data, filename) {
  // 步骤1：分离 MIME 类型和 base64 数据
  const arr = base64Data.split(',')
  const mimeMatch = arr[0].match(/:(.*?);/)
  const mimeType = mimeMatch ? mimeMatch[1] : 'application/octet-stream'
  const b64Data = arr[1] || arr[0] // 处理无头部的 base64

  // 步骤2：解码 Base64 → 二进制
  const byteChars = atob(b64Data)
  const byteNumbers = new Array(byteChars.length)

  for (let i = 0; i < byteChars.length; i++) {
    byteNumbers[i] = byteChars.charCodeAt(i)
  }

  // 步骤3：创建 Blob
  const byteArray = new Uint8Array(byteNumbers)
  const blob = new Blob([byteArray], { type: mimeType })

  // 步骤4：Blob → File
  return new File([blob], filename, {
    type: mimeType,
    lastModified: Date.now()
  })
}

export function getUrlParam(url, name) {
  var pattern = new RegExp('[?&]' + name + '=([^&]+)', 'g')
  var matcher = pattern.exec(url)
  var items = null
  if (matcher !== null) {
    try {
      items = decodeURIComponent(decodeURIComponent(matcher[1]))
    } catch (e) {
      try {
        items = decodeURIComponent(matcher[1])
      } catch (e) {
        items = matcher[1]
      }
    }
  }
  return items
}
