import { MessageBox } from 'element-ui'
import { getNotifyList } from '@/api/deepSeekApi'

export const alertTest = async(disabledTrigger) => {
  const res = await getNotifyList()
  if (res && res.code === 200) {
    const visitFrom = localStorage.getItem('visitFrom')

    const isDTY = visitFrom === 'DTY' || visitFrom === 'MOBILE'
    const info = res.data.filter(item => (isDTY ? item.noticeClient === 'DTY' : item.noticeClient !== 'DTY'))

    console.log('info', info)

    const notify = info[0]

    if (!notify) return
    const start = new Date(notify.noticeStartTime).getTime()
    const end = new Date(notify.noticeEndTime).getTime()
    const now = new Date().getTime()
    if (now >= start && now <= end) {
      MessageBox.alert(`${notify.noticeContent}`, `${notify.noticeTitle}`, {
        dangerouslyUseHTMLString: true,
        showClose: notify.canClosed === 1,
        showConfirmButton: false,
        customClass: disabledTrigger ? 'mobile-message-box' : '',
        callback: () => {
        }
      })
    }
  }
}
